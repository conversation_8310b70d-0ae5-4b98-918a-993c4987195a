<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Correction Popup</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .modern-popup {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            opacity: 0;
            transform: scale(0.9);
            transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        }
        .modern-popup.show {
            opacity: 1;
            transform: scale(1);
        }
        .popup-content {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 20px;
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow: hidden;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .popup-header {
            padding: 25px 30px;
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .popup-close {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2em;
            transition: all 0.3s ease;
        }
        .popup-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: rotate(90deg);
        }
        .popup-body {
            padding: 30px;
            color: #1f2937;
        }
        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(102, 126, 234, 0.1);
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(226, 232, 240, 0.5);
            border-radius: 50px;
            overflow: hidden;
            margin: 24px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            width: 0%;
            transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 50px;
        }
        .log {
            background: #f8f9fa;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Test Correction Popup d'Analyse</h1>
        <p>Ce test simule le comportement corrigé du popup d'analyse.</p>
        
        <div id="logs"></div>
        
        <button onclick="testAnalysisFlow()">🚀 Tester Flux d'Analyse Complet</button>
        <button onclick="testErrorFlow()">❌ Tester Flux d'Erreur</button>
        <button onclick="clearLogs()">🧹 Effacer Logs</button>
    </div>

    <script>
        function log(message) {
            const logs = document.getElementById('logs');
            const logDiv = document.createElement('div');
            logDiv.className = 'log';
            logDiv.textContent = new Date().toLocaleTimeString() + ' - ' + message;
            logs.appendChild(logDiv);
            console.log(message);
        }

        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }

        function closeCurrentPopup() {
            const popups = document.querySelectorAll('.modern-popup');
            popups.forEach(popup => {
                popup.classList.remove('show');
                setTimeout(() => popup.remove(), 300);
            });
            log('✅ Popup fermé');
        }

        function showAnalysisPopupWithLoading() {
            log('🚀 Ouverture du popup d\'analyse avec chargement...');
            
            const loadingPopup = `
                <div class="modern-popup show" id="analysisLoadingPopup">
                    <div class="popup-content">
                        <div class="popup-header" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                            <h2><i class="fas fa-chart-bar"></i> Analyse en cours...</h2>
                            <button class="popup-close" onclick="closeCurrentPopup()">×</button>
                        </div>
                        <div class="popup-body" style="text-align: center; padding: 60px;">
                            <div class="spinner"></div>
                            <h3 style="color: #1f2937; margin-top: 20px;">Analyse complète en cours</h3>
                            <p style="color: #6b7280; margin-top: 10px;">Veuillez patienter pendant que nous analysons votre modèle BIM...</p>
                            <div class="progress-bar" style="margin-top: 30px;">
                                <div class="progress-fill" id="analysisProgress" style="width: 0%;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.insertAdjacentHTML('beforeend', loadingPopup);
            
            // Simuler le progrès
            let progress = 0;
            const progressInterval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 90) progress = 90;
                
                const progressBar = document.getElementById('analysisProgress');
                if (progressBar) {
                    progressBar.style.width = progress + '%';
                }
                
                if (progress >= 90) {
                    clearInterval(progressInterval);
                }
            }, 500);
        }

        function showAnalysisResultsPopup(analysis) {
            log('📊 Affichage des résultats d\'analyse');
            
            const resultsPopup = `
                <div class="modern-popup show" id="analysisResultsPopup">
                    <div class="popup-content">
                        <div class="popup-header" style="background: linear-gradient(135deg, #10b981, #059669);">
                            <h2><i class="fas fa-check-circle"></i> Analyse Terminée</h2>
                            <button class="popup-close" onclick="closeCurrentPopup()">×</button>
                        </div>
                        <div class="popup-body">
                            <h3>✅ Analyse réussie !</h3>
                            <p>L'analyse de votre modèle BIM a été complétée avec succès.</p>
                            <div style="background: #f0fdf4; padding: 15px; border-radius: 8px; margin: 15px 0;">
                                <strong>Résultats simulés :</strong><br>
                                • Éléments analysés: ${analysis.elements || 150}<br>
                                • Anomalies détectées: ${analysis.anomalies || 3}<br>
                                • Classification: ${analysis.type || 'Résidentiel'}<br>
                                • Score PMR: ${analysis.pmr_score || 85}%
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.insertAdjacentHTML('beforeend', resultsPopup);
        }

        function showErrorPopup(errorMessage) {
            log('❌ Affichage de l\'erreur d\'analyse');
            
            const errorPopup = `
                <div class="modern-popup show" id="analysisErrorPopup">
                    <div class="popup-content">
                        <div class="popup-header" style="background: linear-gradient(135deg, #dc2626, #b91c1c);">
                            <h2><i class="fas fa-exclamation-triangle"></i> Erreur d'Analyse</h2>
                            <button class="popup-close" onclick="closeCurrentPopup()">×</button>
                        </div>
                        <div class="popup-body" style="text-align: center; padding: 40px;">
                            <i class="fas fa-times-circle" style="font-size: 3em; color: #dc2626; margin-bottom: 20px;"></i>
                            <h3 style="color: #dc2626; margin-bottom: 15px;">Erreur lors de l'analyse</h3>
                            <div style="background: #fef2f2; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: left;">
                                <code style="color: #991b1b; font-size: 0.9em;">${errorMessage}</code>
                            </div>
                            <p style="color: #64748b;">Vérifiez que le backend est démarré et que le fichier IFC est valide.</p>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.insertAdjacentHTML('beforeend', errorPopup);
        }

        function updateEnhancedAnalysisPopup(analysis) {
            log('🔍 Mise à jour du popup enrichi avec les résultats');

            // Fermer d'abord le popup de chargement
            const loadingPopup = document.getElementById('analysisLoadingPopup');
            if (loadingPopup) {
                loadingPopup.remove();
                log('✅ Popup de chargement fermé');
            }

            // Afficher les résultats dans un nouveau popup
            showAnalysisResultsPopup(analysis);
        }

        function updateAnalysisPopupWithError(errorMessage) {
            log('❌ Gestion de l\'erreur d\'analyse');

            // Fermer d'abord le popup de chargement
            const loadingPopup = document.getElementById('analysisLoadingPopup');
            if (loadingPopup) {
                loadingPopup.remove();
                log('✅ Popup de chargement fermé (erreur)');
            }

            // Afficher l'erreur
            showErrorPopup(errorMessage);
        }

        async function testAnalysisFlow() {
            log('🧪 === TEST FLUX D\'ANALYSE RÉUSSI ===');
            
            // 1. Afficher le popup de chargement
            showAnalysisPopupWithLoading();
            
            // 2. Simuler une analyse qui réussit après 3 secondes
            setTimeout(() => {
                const mockAnalysis = {
                    elements: 150,
                    anomalies: 3,
                    type: 'Résidentiel',
                    pmr_score: 85
                };
                updateEnhancedAnalysisPopup(mockAnalysis);
            }, 3000);
        }

        async function testErrorFlow() {
            log('🧪 === TEST FLUX D\'ERREUR ===');
            
            // 1. Afficher le popup de chargement
            showAnalysisPopupWithLoading();
            
            // 2. Simuler une erreur après 2 secondes
            setTimeout(() => {
                updateAnalysisPopupWithError('Erreur de connexion au backend - Impossible de traiter le fichier IFC');
            }, 2000);
        }
    </script>
</body>
</html>
