<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test des Fonctions BIM Analysis</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Test des Fonctions BIM Analysis</h1>
        <p>Ce fichier teste si les fonctions manquantes ont été correctement ajoutées.</p>
        
        <div id="results"></div>
        
        <button onclick="testFunctions()">🚀 Lancer les Tests</button>
        <button onclick="testAnalyzeFile()">📊 Tester analyzeFile()</button>
        <button onclick="testLoadFileList()">📁 Tester loadFileList()</button>
        <button onclick="testShowAnalysisPopup()">🔍 Tester showAnalysisPopupWithLoading()</button>
    </div>

    <script>
        // Simuler les fonctions de base nécessaires
        let currentFile = { name: 'test.ifc' };
        
        function getCache(type) {
            return null; // Simuler pas de cache
        }
        
        function closeCurrentPopup() {
            const popups = document.querySelectorAll('.modern-popup');
            popups.forEach(popup => popup.remove());
        }
        
        // Copier les fonctions corrigées du fichier principal
        function loadFileList() {
            console.log('📁 Chargement de la liste des fichiers...');
            // Cette fonction peut être étendue pour charger une liste de fichiers depuis le serveur
            // Pour l'instant, elle ne fait rien mais évite l'erreur ReferenceError
        }

        function showAnalysisPopupWithLoading() {
            console.log('🚀 Ouverture du popup d\'analyse avec chargement...');
            
            // Créer et afficher un popup de chargement
            const loadingPopup = `
                <div class="modern-popup show" id="analysisLoadingPopup" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); display: flex; justify-content: center; align-items: center; z-index: 10000;">
                    <div class="popup-content" style="background: white; border-radius: 20px; max-width: 500px; width: 90%; padding: 0; overflow: hidden;">
                        <div class="popup-header" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; padding: 20px; display: flex; justify-content: space-between; align-items: center;">
                            <h2 style="margin: 0;"><i class="fas fa-chart-bar"></i> Analyse en cours...</h2>
                            <button class="popup-close" onclick="closeCurrentPopup()" style="background: rgba(255,255,255,0.2); border: none; color: white; width: 30px; height: 30px; border-radius: 50%; cursor: pointer;">×</button>
                        </div>
                        <div class="popup-body" style="text-align: center; padding: 60px;">
                            <div class="spinner" style="width: 50px; height: 50px; border: 4px solid #f3f3f3; border-top: 4px solid #3b82f6; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 20px;"></div>
                            <h3 style="color: #1f2937; margin-top: 20px;">Analyse complète en cours</h3>
                            <p style="color: #6b7280; margin-top: 10px;">Veuillez patienter pendant que nous analysons votre modèle BIM...</p>
                            <div class="progress-bar" style="width: 100%; height: 8px; background: #e5e7eb; border-radius: 4px; overflow: hidden; margin-top: 30px;">
                                <div class="progress-fill" id="analysisProgress" style="height: 100%; background: linear-gradient(135deg, #3b82f6, #1d4ed8); width: 0%; transition: width 0.5s;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.insertAdjacentHTML('beforeend', loadingPopup);
            
            // Simuler le progrès
            let progress = 0;
            const progressInterval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 90) progress = 90;
                
                const progressBar = document.getElementById('analysisProgress');
                if (progressBar) {
                    progressBar.style.width = progress + '%';
                }
                
                if (progress >= 90) {
                    clearInterval(progressInterval);
                }
            }, 500);
        }

        async function analyzeFile() {
            if (!currentFile) return;

            // 🚀 NOUVEAU: Ouvrir le pop-up immédiatement avec état de chargement
            showAnalysisPopupWithLoading();

            // Vérifier le cache d'abord
            const cachedResult = getCache('comprehensive');
            if (cachedResult) {
                console.log('⚡ Utilisation des données en cache pour l\'analyse complète');
                return cachedResult;
            }

            console.log('🔍 Analyse du fichier:', currentFile.name);
            
            // Simuler une analyse
            setTimeout(() => {
                closeCurrentPopup();
                addResult('✅ analyzeFile() exécutée avec succès', 'success');
            }, 3000);
        }

        function triggerRandomPulse() {
            const cards = document.querySelectorAll('.dashboard-card');
            if (cards.length === 0) return; // Éviter l'erreur si aucune carte n'existe
            
            const randomCard = cards[Math.floor(Math.random() * cards.length)];
            if (randomCard && randomCard.classList) {
                randomCard.classList.add('pulse-active');
                setTimeout(() => {
                    if (randomCard && randomCard.classList) {
                        randomCard.classList.remove('pulse-active');
                    }
                }, 2000);
            }
        }

        // Fonctions de test
        function addResult(message, type) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            results.appendChild(div);
        }

        function testFunctions() {
            document.getElementById('results').innerHTML = '';
            
            // Test loadFileList
            try {
                loadFileList();
                addResult('✅ loadFileList() - Fonction définie et exécutable', 'success');
            } catch (error) {
                addResult('❌ loadFileList() - Erreur: ' + error.message, 'error');
            }

            // Test showAnalysisPopupWithLoading
            try {
                if (typeof showAnalysisPopupWithLoading === 'function') {
                    addResult('✅ showAnalysisPopupWithLoading() - Fonction définie', 'success');
                } else {
                    addResult('❌ showAnalysisPopupWithLoading() - Fonction non définie', 'error');
                }
            } catch (error) {
                addResult('❌ showAnalysisPopupWithLoading() - Erreur: ' + error.message, 'error');
            }

            // Test triggerRandomPulse
            try {
                triggerRandomPulse();
                addResult('✅ triggerRandomPulse() - Fonction définie et exécutable (pas de cartes trouvées, mais pas d\'erreur)', 'success');
            } catch (error) {
                addResult('❌ triggerRandomPulse() - Erreur: ' + error.message, 'error');
            }

            // Test analyzeFile
            try {
                if (typeof analyzeFile === 'function') {
                    addResult('✅ analyzeFile() - Fonction définie', 'success');
                } else {
                    addResult('❌ analyzeFile() - Fonction non définie', 'error');
                }
            } catch (error) {
                addResult('❌ analyzeFile() - Erreur: ' + error.message, 'error');
            }
        }

        function testAnalyzeFile() {
            analyzeFile();
        }

        function testLoadFileList() {
            try {
                loadFileList();
                addResult('✅ loadFileList() exécutée avec succès', 'success');
            } catch (error) {
                addResult('❌ loadFileList() - Erreur: ' + error.message, 'error');
            }
        }

        function testShowAnalysisPopup() {
            try {
                showAnalysisPopupWithLoading();
                addResult('✅ showAnalysisPopupWithLoading() exécutée avec succès', 'success');
            } catch (error) {
                addResult('❌ showAnalysisPopupWithLoading() - Erreur: ' + error.message, 'error');
            }
        }
    </script>

    <style>
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</body>
</html>
