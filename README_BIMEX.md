# 🚀 BIMEX 2.0 - Centre de Contrôle BIM

## 📋 Instructions de Démarrage

### 🔧 Option 1: <PERSON><PERSON> (Mode Production)

#### Windows:
```bash
# Double-cliquez sur le fichier ou exécutez dans le terminal
start_backend.bat
```

#### Linux/Mac:
```bash
# Rendez le script exécutable et lancez-le
chmod +x start_backend.sh
./start_backend.sh
```

#### Manuel:
```bash
cd backend
pip install -r requirements.txt
python main.py
```

**<PERSON><PERSON><PERSON> accédez à:** `http://localhost:8000/frontend/bim_analysis.html`

### 🎭 Option 2: Mode Démo (Sans Backend)

Si le backend n'est pas disponible, l'interface passera automatiquement en **Mode Démo** avec des données simulées.

**Accédez directement à:** `file:///chemin/vers/frontend/bim_analysis.html`

## 🎯 Fonctionnalités

### ✅ Mode Production (Backend Actif)
- 🔄 Analyses BIM réelles
- 📊 Données authentiques
- 🤖 IA conversationnelle
- 📈 Métriques temps réel
- 💾 Sauvegarde des résultats

### 🎭 Mode Démo (Backend Inactif)
- 🎲 Données simulées réalistes
- 🎨 Interface complètement fonctionnelle
- 📊 Visualisations dynamiques
- ⚡ Réponses instantanées
- 🔍 Toutes les fonctionnalités UI

## 🔍 Diagnostic Système

### Dans la Console du Navigateur:
```javascript
// Vérifier l'état du système
runSystemDiagnostics();

// Vérifier le backend
checkBackendAvailability();

// Variables d'état
console.log('Backend:', backendAvailable);
console.log('Mode démo:', demoMode);
```

## 🚨 Résolution des Problèmes

### ❌ "Failed to fetch" dans les pop-ups
**Solution:** Le système passe automatiquement en mode démo
- ✅ Toutes les fonctionnalités restent disponibles
- 🎭 Données simulées mais réalistes
- 🔄 Redémarrez le backend pour le mode production

### ⚠️ Fonctions manquantes
**Solution:** Vérifiez la console pour le diagnostic
```javascript
runSystemDiagnostics();
```

### 🔧 Backend ne démarre pas
**Vérifiez:**
1. Python est installé (`python --version`)
2. Les dépendances sont installées (`pip install -r requirements.txt`)
3. Le port 8000 est libre
4. Aucun antivirus ne bloque l'exécution

## 📊 Sections Disponibles

### 🎯 Workflow n8n
- Graphique interactif avec nœuds connectés
- Exécution séquentielle des analyses
- Suivi temps réel de la progression
- Gestion d'erreurs intelligente

### 📈 Visualisations Grafana
- Métriques temps réel
- Graphiques interactifs (Time Series, Gauge, Heatmap)
- Query Builder avancé
- Export de données

### 🖥️ Monitoring Pro
- Surveillance système (CPU, RAM, Réseau)
- Graphiques de performance
- Dashboard complet
- Alertes automatiques

### 🧠 Analytics Pro
- Questions en langage naturel
- Réponses IA contextuelles
- Visualisations automatiques
- Workspace avancé

## 🎨 Interface

### 🌟 Design Moderne
- Thème cyberpunk avec effets néon
- Animations fluides
- Interface responsive
- Notifications contextuelles

### 🎮 Interactions
- Boutons avec feedback visuel
- Pop-ups modernes
- Transitions animées
- États de chargement

## 📝 Notes Techniques

- **Frontend:** HTML5, CSS3, JavaScript ES6+
- **Backend:** Python FastAPI
- **Visualisations:** Canvas API, Chart.js style
- **Architecture:** API REST avec fallback démo
- **Compatibilité:** Navigateurs modernes (Chrome, Firefox, Edge)

## 🆘 Support

En cas de problème:
1. Vérifiez la console du navigateur (F12)
2. Exécutez `runSystemDiagnostics()`
3. Consultez les logs du backend
4. Le mode démo fonctionne toujours en fallback

---

**BIMEX 2.0** - Intelligence Artificielle pour l'Analyse BIM Avancée 🚀
