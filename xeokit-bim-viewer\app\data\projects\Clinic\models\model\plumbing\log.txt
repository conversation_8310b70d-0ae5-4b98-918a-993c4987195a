#----------------------------------------------------------------------------
# CxConverter IFC Conversion Pipeline Log
#
# Converting file: Clinic_Plumbing.ifc
# Using tools: ifc2gltf and convert2xkt
# Date: Tue Dec 19 2023 17:07:32 GMT+0100 (Central European Standard Time)
#----------------------------------------------------------------------------



# ~/xeolabs/xeokit/ifc2gltf/3_0_6/ifc2gltfcxconverter-3.0.6/linux/bin/ifc2gltfcxconverter -i /home/<USER>/xeolabs/xeokit-model-conversion-tests-sep8/inputFiles/Clinic/Clinic_Plumbing.ifc -o /home/<USER>/xeolabs/xeokit-model-conversion-tests-sep8/results/Clinic/Clinic_Plumbing/ifcCXConverterPipeline1/model.glb -m /home/<USER>/xeolabs/xeokit-model-conversion-tests-sep8/results/Clinic/Clinic_Plumbing/ifcCXConverterPipeline1/model.json -s 10 -e 3 >> /home/<USER>/xeolabs/xeokit-model-conversion-tests-sep8/results/Clinic/Clinic_Plumbing/ifcCXConverterPipeline1/log.txt

__ IFC to GLTF converter __ version: 3.0.6 ____________
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc"
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 5%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 6%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 7%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 8%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 9%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 10%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 11%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 12%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 13%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 14%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 15%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 16%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 17%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 18%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 19%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 20%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 21%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 22%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 23%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 24%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 30%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 33%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 36%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 39%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 42%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 45%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 48%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 51%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 54%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 57%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 60%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 63%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 66%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 69%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 72%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 75%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 78%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 81%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 84%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 87%
Loading file "/home/<USER>/xe...Clinic_Plumbing.ifc": 100%
Converting IFC geometry
Converting IFC geometry: 0%
Converting IFC geometry: 0%
Converting IFC geometry: 1%
Converting IFC geometry: 2%
Converting IFC geometry: 3%
Converting IFC geometry: 4%
Converting IFC geometry: 5%
Converting IFC geometry: 6%
Converting IFC geometry: 7%
Converting IFC geometry: 8%
Converting IFC geometry: 9%
Converting IFC geometry: 9%
Converting IFC geometry: 10%
Converting IFC geometry: 11%
Converting IFC geometry: 12%
Converting IFC geometry: 13%
Converting IFC geometry: 14%
Converting IFC geometry: 15%
Converting IFC geometry: 16%
Converting IFC geometry: 17%
Converting IFC geometry: 18%
Converting IFC geometry: 18%
Converting IFC geometry: 19%
Converting IFC geometry: 20%
Converting IFC geometry: 21%
Converting IFC geometry: 22%
Converting IFC geometry: 23%
Converting IFC geometry: 24%
Converting IFC geometry: 25%
Converting IFC geometry: 26%
Converting IFC geometry: 27%
Converting IFC geometry: 27%
Converting IFC geometry: 28%
Converting IFC geometry: 29%
Converting IFC geometry: 30%
Converting IFC geometry: 31%
Converting IFC geometry: 32%
Converting IFC geometry: 33%
Converting IFC geometry: 34%
Converting IFC geometry: 35%
Converting IFC geometry: 36%
Converting IFC geometry: 36%
Converting IFC geometry: 37%
Converting IFC geometry: 38%
Converting IFC geometry: 39%
Converting IFC geometry: 40%
Converting IFC geometry: 41%
Converting IFC geometry: 42%
Converting IFC geometry: 43%
Converting IFC geometry: 44%
Converting IFC geometry: 45%
Converting IFC geometry: 46%
Converting IFC geometry: 46%
Converting IFC geometry: 47%
Converting IFC geometry: 48%
Converting IFC geometry: 49%
Converting IFC geometry: 50%
Converting IFC geometry: 51%
Converting IFC geometry: 52%
Converting IFC geometry: 53%
Converting IFC geometry: 54%
Converting IFC geometry: 55%
Converting IFC geometry: 55%
Converting IFC geometry: 56%
Converting IFC geometry: 57%
Converting IFC geometry: 58%
Converting IFC geometry: 59%
Converting IFC geometry: 60%
Converting IFC geometry: 61%
Converting IFC geometry: 62%
Converting IFC geometry: 63%
Converting IFC geometry: 64%
Converting IFC geometry: 64%
Converting IFC geometry: 65%
Converting IFC geometry: 66%
Converting IFC geometry: 67%
Converting IFC geometry: 68%
Converting IFC geometry: 69%
Converting IFC geometry: 70%
Converting IFC geometry: 71%
Converting IFC geometry: 72%
Converting IFC geometry: 73%
Converting IFC geometry: 73%
Converting IFC geometry: 74%
Converting IFC geometry: 75%
Converting IFC geometry: 76%
Converting IFC geometry: 77%
Converting IFC geometry: 78%
Converting IFC geometry: 79%
Converting IFC geometry: 80%
Converting IFC geometry: 81%
Converting IFC geometry: 82%
Converting IFC geometry: 83%
Converting IFC geometry: 83%
Converting IFC geometry: 84%
Converting IFC geometry: 85%
Converting IFC geometry: 86%
Converting IFC geometry: 87%
Converting IFC geometry: 88%
Converting IFC geometry: 89%
Converting IFC geometry: 100%
Exporting to gltf
Exporting to gltf: 50%
Exporting to gltf: 51%
Exporting to gltf: 53%
Exporting to gltf: 54%
Exporting to gltf: 56%
Exporting to gltf: 57%
Exporting to gltf: 59%
Exporting to gltf: 60%
Exporting to gltf: 100%
Accessors: 2542, reused: 7780
Exporting meta data
Exporting meta data: 75%
Exporting meta data: 76%
Exporting meta data: 78%
Exporting meta data: 79%
Exporting meta data: 81%
Exporting meta data: 82%
Exporting meta data: 84%
Exporting meta data: 85%
Exporting meta data: 87%
Exporting meta data: 88%
Exporting meta data: 90%
Exporting meta data: 91%
Exporting meta data: 93%
Exporting meta data: 94%
Exporting meta data: 100%
Exporting meta data: 50%
Exporting meta data: 51%
Exporting meta data: 53%
Exporting meta data: 54%
Exporting meta data: 56%
Exporting meta data: 57%
Exporting meta data: 59%
Exporting meta data: 60%
Exporting meta data: 62%
Exporting meta data: 63%
Exporting meta data: 100%
Accessors: 1295, reused: 2267
Exporting meta data
Exporting meta data: 75%
Exporting meta data: 76%
Exporting meta data: 78%
Exporting meta data: 79%
Exporting meta data: 81%
Exporting meta data: 82%
Exporting meta data: 84%
Exporting meta data: 85%
Exporting meta data: 87%
Exporting meta data: 88%
Exporting meta data: 90%
Exporting meta data: 91%
Exporting meta data: 93%
Exporting meta data: 94%
Exporting meta data: 100%
done in 15 seconds



# node --max-old-space-size=24000 ~/xeolabs/xeokit-convert-aug9/convert2xkt.js -t -n -a results/Clinic/Clinic_Plumbing/ifcCXConverterPipeline1/model.glb.manifest.json -o results/Clinic/Clinic_Plumbing/ifcCXConverterPipeline1/model.xkt.manifest.json -l >> /home/<USER>/xeolabs/xeokit-model-conversion-tests-sep8/results/Clinic/Clinic_Plumbing/ifcCXConverterPipeline1/log.txt
[convert2xkt] Running convert2xkt v1.1.11...
[convert2xkt] Converting glTF files in manifest results/Clinic/Clinic_Plumbing/ifcCXConverterPipeline1/model.glb.manifest.json...
[convert2xkt] Reading input file: /home/<USER>/xeolabs/xeokit-model-conversion-tests-sep8/results/Clinic/Clinic_Plumbing/ifcCXConverterPipeline1/model.glb
[convert2xkt] Input file size: 12540.39 kB
[convert2xkt] Reading input metadata file: /home/<USER>/xeolabs/xeokit-model-conversion-tests-sep8/results/Clinic/Clinic_Plumbing/ifcCXConverterPipeline1/model.json
[convert2xkt] Using parser: parseGLTFIntoXKTModel
[convert2xkt] Parsing normals: enabled
[convert2xkt] Parsing textures: enabled
[convert2xkt] Input file parsed OK. Building XKT document...
[convert2xkt] XKT document built OK. Writing to XKT file...
[convert2xkt] Converted to: XKT v10
[convert2xkt] XKT size: 7690.77 kB
[convert2xkt] XKT textures size: 0.01kB
[convert2xkt] Compression ratio: 1.63
[convert2xkt] Conversion time: 11.21 s
[convert2xkt] Converted metaobjects: 0
[convert2xkt] Converted property sets: 0
[convert2xkt] Converted drawable objects: 4910
[convert2xkt] Converted geometries: 7888
[convert2xkt] Converted textures: 0
[convert2xkt] Converted textureSets: 0
[convert2xkt] Converted triangles: 1556454
[convert2xkt] Converted vertices: 4669362
[convert2xkt] Converted UVs: 0
[convert2xkt] Converted normals: 0
[convert2xkt] Number of tiles: 1
[convert2xkt] minTileSize: 500
[convert2xkt] Writing XKT file: results/Clinic/Clinic_Plumbing/ifcCXConverterPipeline1/model.xkt
[convert2xkt] Converted model1.xkt (1 of 2)
[convert2xkt] Reading input file: /home/<USER>/xeolabs/xeokit-model-conversion-tests-sep8/results/Clinic/Clinic_Plumbing/ifcCXConverterPipeline1/model_1.glb
[convert2xkt] Input file size: 7228.72 kB
[convert2xkt] Reading input metadata file: /home/<USER>/xeolabs/xeokit-model-conversion-tests-sep8/results/Clinic/Clinic_Plumbing/ifcCXConverterPipeline1/model_1.json
[convert2xkt] Using parser: parseGLTFIntoXKTModel
[convert2xkt] Parsing normals: enabled
[convert2xkt] Parsing textures: enabled
[convert2xkt] Input file parsed OK. Building XKT document...
[convert2xkt] XKT document built OK. Writing to XKT file...
[convert2xkt] Converted to: XKT v10
[convert2xkt] XKT size: 11793.23 kB
[convert2xkt] XKT textures size: 0.01kB
[convert2xkt] Compression ratio: 0.61
[convert2xkt] Conversion time: 13.11 s
[convert2xkt] Converted metaobjects: 0
[convert2xkt] Converted property sets: 0
[convert2xkt] Converted drawable objects: 1676
[convert2xkt] Converted geometries: 2315
[convert2xkt] Converted textures: 0
[convert2xkt] Converted textureSets: 0
[convert2xkt] Converted triangles: 1656535
[convert2xkt] Converted vertices: 4969605
[convert2xkt] Converted UVs: 0
[convert2xkt] Converted normals: 0
[convert2xkt] Number of tiles: 1
[convert2xkt] minTileSize: 500
[convert2xkt] Writing XKT file: results/Clinic/Clinic_Plumbing/ifcCXConverterPipeline1/model_1.xkt
[convert2xkt] Converted model2.xkt (2 of 2)
[convert2xkt] Done.
node --max-old-space-size=24000 ~/xeolabs/xeokit-convert-aug9/convert2xkt.js -t -n -a results/Clinic/Clinic_Plumbing/ifcCXConverterPipeline1/model.glb.manifest.json -o results/Clinic/Clinic_Plumbing/ifcCXConverterPipeline1/model.xkt.manifest.json -l >> /home/<USER>/xeolabs/xeokit-model-conversion-tests-sep8/results/Clinic/Clinic_Plumbing/ifcCXConverterPipeline1/log.txt
