<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>BIMEX 2.0 - Mission Control Center</title>
    <link rel="stylesheet" href="./lib/fontawesome-free-5.11.2-web/css/all.min.css" type="text/css" />
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            /* Couleurs futuristes BIMEX 2.0 */
            --primary-neon: #00f5ff;
            --secondary-neon: #ff0080;
            --success-neon: #00ff88;
            --warning-neon: #ffaa00;
            --danger-neon: #ff3366;
            --purple-neon: #8b5cf6;

            /* Arrière-plans sombres */
            --bg-dark: #0a0a0f;
            --bg-card: #1a1a2e;
            --bg-glass: rgba(26, 26, 46, 0.8);
            --bg-hover: rgba(0, 245, 255, 0.1);

            /* Texte */
            --text-primary: #ffffff;
            --text-secondary: #a0a0b0;
            --text-muted: #606070;

            /* Effets */
            --glow-primary: 0 0 20px rgba(0, 245, 255, 0.5);
            --glow-secondary: 0 0 20px rgba(255, 0, 128, 0.5);
            --glow-success: 0 0 20px rgba(0, 255, 136, 0.5);

            /* Animations */
            --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-smooth: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

            /* 🚀 BUSINESS INTELLIGENCE COLORS */
            --bi-primary: #6366f1;
            --bi-secondary: #8b5cf6;
            --bi-accent: #ec4899;
            --bi-success: #10b981;
            --bi-warning: #f59e0b;
            --bi-gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);
            --bi-glow: 0 0 30px rgba(99, 102, 241, 0.4);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--bg-dark);
            min-height: 100vh;
            padding: 20px;
            color: var(--text-primary);
            position: relative;
            overflow-x: hidden;
        }

        /* Arrière-plan animé futuriste */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(0, 245, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 0, 128, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
            z-index: -2;
            animation: backgroundPulse 8s ease-in-out infinite;
        }

        @keyframes backgroundPulse {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.6; }
        }

        /* Grille de fond style Matrix/Cyberpunk */
        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                linear-gradient(rgba(0, 245, 255, 0.03) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 245, 255, 0.03) 1px, transparent 1px);
            background-size: 50px 50px;
            z-index: -1;
            animation: gridMove 20s linear infinite;
        }

        @keyframes gridMove {
            0% { transform: translate(0, 0); }
            100% { transform: translate(50px, 50px); }
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 50px;
            position: relative;
            flex-wrap: wrap;
            gap: 20px;
        }

        .logo-container {
            display: flex;
            align-items: center;
            gap: 25px;
            flex-wrap: wrap;
        }

        .logo-image {
            width: 100px;
            height: 100px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            transition: transform 0.3s ease;
        }

        .logo-image:hover {
            transform: scale(1.1) rotate(5deg);
        }

        .brand-text {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }

        /* Status système en temps réel */
        .system-status {
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background: var(--bg-glass);
            border-radius: 20px;
            border: 1px solid rgba(0, 245, 255, 0.2);
            font-family: 'JetBrains Mono', monospace;
            font-size: 12px;
            color: var(--text-secondary);
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--success-neon);
            box-shadow: 0 0 10px var(--success-neon);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .logo {
            font-size: 4em;
            margin-bottom: 20px;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-15px); }
        }

        .hero-title {
            font-size: 4em;
            font-weight: 800;
            margin-bottom: 10px;
            background: linear-gradient(135deg, var(--primary-neon), var(--secondary-neon));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 20px rgba(0, 245, 255, 0.5);
            animation: titleGlow 3s ease-in-out infinite;
        }

        @keyframes titleGlow {
            0%, 100% { filter: drop-shadow(0 0 10px rgba(0, 245, 255, 0.3)); }
            50% { filter: drop-shadow(0 0 20px rgba(255, 0, 128, 0.5)); }
        }

        .hero-subtitle {
            font-size: 1.4em;
            margin-bottom: 30px;
            color: var(--text-secondary);
            line-height: 1.6;
            font-weight: 400;
            letter-spacing: 0.5px;
            font-family: 'JetBrains Mono', monospace;
            text-transform: uppercase;
        }

        .search-section {
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 1px solid rgba(0, 245, 255, 0.2);
            padding: 30px;
            margin-bottom: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .search-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-neon), var(--secondary-neon));
            animation: progressGlow 3s ease-in-out infinite;
        }

        @keyframes progressGlow {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }

        .add-model-section {
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 1px solid rgba(0, 255, 136, 0.2);
            padding: 30px;
            margin-bottom: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .add-model-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--success-neon), var(--primary-neon));
            animation: progressGlow 3s ease-in-out infinite;
        }

        .add-model-btn {
            background: linear-gradient(135deg, var(--success-neon), var(--primary-neon));
            color: white;
            border: none;
            padding: 18px 36px;
            border-radius: 12px;
            font-size: 1.2em;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-smooth);
            box-shadow: var(--glow-success);
            display: inline-flex;
            align-items: center;
            gap: 12px;
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .add-model-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: var(--transition-smooth);
        }

        .add-model-btn:hover::before {
            left: 100%;
        }

        .add-model-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 0 30px rgba(0, 255, 136, 0.7);
        }

        .search-box {
            background: white;
            border-radius: 50px;
            padding: 15px 25px;
            margin: 0 auto;
            max-width: 500px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
        }

        .search-box input {
            border: none;
            outline: none;
            width: 100%;
            font-size: 1.1em;
            background: transparent;
            margin-left: 15px;
        }

        .search-box i {
            color: #667eea;
        }

        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .project-card {
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
            transition: var(--transition-smooth);
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(0, 245, 255, 0.2);
        }

        .project-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--glow-primary);
            border-color: var(--primary-neon);
        }

        .project-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-neon), var(--secondary-neon));
            transform: scaleX(0);
            transition: var(--transition-smooth);
        }

        .project-card:hover::before {
            transform: scaleX(1);
        }

        .project-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .project-icon {
            font-size: 2.5em;
            color: var(--primary-neon);
            margin-right: 15px;
            text-shadow: 0 0 15px rgba(0, 245, 255, 0.5);
            animation: iconGlow 3s ease-in-out infinite;
        }

        @keyframes iconGlow {
            0%, 100% { color: var(--primary-neon); }
            50% { color: var(--secondary-neon); }
        }

        .project-info h3 {
            font-size: 1.4em;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 5px;
            background: linear-gradient(135deg, var(--primary-neon), var(--secondary-neon));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .project-info .project-id {
            font-size: 0.9em;
            color: var(--text-secondary);
            font-family: 'JetBrains Mono', monospace;
        }

        .project-actions {
            display: flex;
            gap: 15px;
            align-items: center;
            justify-content: space-between;
        }

        .main-action {
            flex: 1;
            min-width: 200px;
            font-size: 1em;
            padding: 15px 20px;
        }

        /* Menu contextuel innovant */
        .action-menu-container {
            position: relative;
        }

        .action-menu-trigger {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--bg-glass);
            border: 2px solid rgba(0, 245, 255, 0.3);
            color: var(--text-primary);
            cursor: pointer;
            transition: var(--transition-smooth);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            backdrop-filter: blur(10px);
        }

        .action-menu-trigger:hover {
            border-color: var(--primary-neon);
            box-shadow: var(--glow-primary);
            transform: scale(1.1);
        }

        .action-menu {
            position: absolute;
            top: 60px;
            right: 0;
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            border: 1px solid rgba(0, 245, 255, 0.2);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            min-width: 250px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px) scale(0.95);
            transition: var(--transition-smooth);
            z-index: 1000;
        }

        .action-menu.active {
            opacity: 1;
            visibility: visible;
            transform: translateY(0) scale(1);
        }

        .action-menu-item {
            padding: 15px 20px;
            cursor: pointer;
            transition: var(--transition-fast);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .action-menu-item:last-child {
            border-bottom: none;
        }

        .action-menu-item:hover {
            background: rgba(0, 245, 255, 0.1);
            transform: translateX(5px);
        }

        .action-menu-item.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .action-menu-item.disabled:hover {
            background: none;
            transform: none;
        }

        .action-menu-item i {
            width: 20px;
            color: var(--primary-neon);
            font-size: 16px;
        }

        .action-menu-item span {
            color: var(--text-primary);
            font-weight: 600;
            flex: 1;
        }

        .action-menu-item small {
            color: var(--text-secondary);
            font-size: 12px;
        }

        /* 🚀 BUSINESS INTELLIGENCE MODULE RÉVOLUTIONNAIRE */
        .bi-control-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
        }

        .bi-trigger-btn {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background: var(--bi-gradient);
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: var(--bi-glow);
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
            animation: biPulse 3s infinite;
        }

        @keyframes biPulse {
            0%, 100% {
                box-shadow: var(--bi-glow);
                transform: scale(1);
            }
            50% {
                box-shadow: 0 0 50px rgba(99, 102, 241, 0.8);
                transform: scale(1.05);
            }
        }

        .bi-trigger-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.5s;
        }

        .bi-trigger-btn:hover::before {
            left: 100%;
        }

        .bi-trigger-btn:hover {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 0 60px rgba(99, 102, 241, 1);
        }

        .bi-dashboard {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(10, 10, 15, 0.95);
            backdrop-filter: blur(20px);
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            overflow-y: auto;
        }

        .bi-dashboard.active {
            opacity: 1;
            visibility: visible;
        }

        .bi-dashboard-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
            animation: biSlideIn 0.6s ease-out;
        }

        @keyframes biSlideIn {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .bi-header {
            text-align: center;
            margin-bottom: 50px;
            position: relative;
        }

        .bi-title {
            font-size: 3.5em;
            font-weight: 800;
            background: var(--bi-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
            animation: biTitleGlow 2s ease-in-out infinite alternate;
        }

        @keyframes biTitleGlow {
            from { filter: drop-shadow(0 0 10px rgba(99, 102, 241, 0.5)); }
            to { filter: drop-shadow(0 0 20px rgba(99, 102, 241, 0.8)); }
        }

        .bi-subtitle {
            color: var(--text-secondary);
            font-size: 1.2em;
            margin-bottom: 30px;
        }

        .bi-close-btn {
            position: absolute;
            top: -20px;
            right: 0;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 20px;
            cursor: pointer;
            transition: var(--transition-smooth);
        }

        .bi-close-btn:hover {
            background: rgba(255, 0, 128, 0.2);
            border-color: var(--secondary-neon);
            transform: rotate(90deg);
        }

        /* 📊 WIDGETS BI RÉVOLUTIONNAIRES */
        .bi-widgets-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }

        .bi-widget {
            background: rgba(26, 26, 46, 0.8);
            border-radius: 20px;
            padding: 30px;
            border: 1px solid rgba(99, 102, 241, 0.3);
            backdrop-filter: blur(15px);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }

        .bi-widget::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent, rgba(99, 102, 241, 0.1), transparent);
            transform: translateX(-100%);
            transition: transform 0.6s;
        }

        .bi-widget:hover::before {
            transform: translateX(100%);
        }

        .bi-widget:hover {
            transform: translateY(-10px) scale(1.02);
            border-color: var(--bi-primary);
            box-shadow: 0 20px 40px rgba(99, 102, 241, 0.3);
        }

        .bi-widget-header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            position: relative;
            z-index: 2;
        }

        .bi-widget-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            background: var(--bi-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 20px;
            color: white;
            animation: iconRotate 4s linear infinite;
        }

        @keyframes iconRotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .bi-widget-title {
            font-size: 1.4em;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 5px;
        }

        .bi-widget-subtitle {
            color: var(--text-secondary);
            font-size: 0.9em;
        }

        .bi-widget-content {
            position: relative;
            z-index: 2;
        }

        .bi-status-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--bi-success);
            box-shadow: 0 0 10px var(--bi-success);
            animation: statusBlink 2s infinite;
            z-index: 3;
        }

        @keyframes statusBlink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.3; }
        }

        /* 🔗 CONNECTEURS TEMPS RÉEL */
        .bi-connectors {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .bi-connector {
            background: rgba(26, 26, 46, 0.6);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: var(--transition-smooth);
            position: relative;
        }

        .bi-connector:hover {
            border-color: var(--bi-primary);
            transform: translateY(-5px);
        }

        .bi-connector-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .bi-connector-logo {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: white;
        }

        .bi-connector-logo.powerbi {
            background: linear-gradient(135deg, #f2c811 0%, #e6a500 100%);
        }

        .bi-connector-logo.tableau {
            background: linear-gradient(135deg, #1f77b4 0%, #ff7f0e 100%);
        }

        .bi-connector-logo.n8n {
            background: linear-gradient(135deg, #ea4b71 0%, #ff6b6b 100%);
        }

        .bi-connector-logo.erp {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .bi-connector-name {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 5px;
        }

        .bi-connector-status {
            font-size: 0.8em;
            color: var(--bi-success);
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .bi-connector-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .bi-action-btn {
            flex: 1;
            padding: 8px 16px;
            border: 1px solid rgba(99, 102, 241, 0.5);
            background: rgba(99, 102, 241, 0.1);
            color: var(--bi-primary);
            border-radius: 8px;
            cursor: pointer;
            transition: var(--transition-fast);
            font-size: 0.9em;
        }

        .bi-action-btn:hover {
            background: rgba(99, 102, 241, 0.2);
            border-color: var(--bi-primary);
        }

        .action-btn {
            flex: 1;
            min-width: 120px;
            padding: 12px 15px;
            border: none;
            border-radius: 8px;
            font-size: 0.9em;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .action-btn.primary {
            background: linear-gradient(135deg, var(--primary-neon), var(--secondary-neon));
            color: white;
            box-shadow: var(--glow-primary);
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .action-btn.primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .action-btn.primary:hover::before {
            left: 100%;
        }

        .action-btn.primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 0 30px rgba(0, 245, 255, 0.7);
        }

        .action-btn.secondary {
            background: transparent;
            color: var(--text-primary);
            border: 2px solid rgba(0, 245, 255, 0.3);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .action-btn.secondary:hover {
            border-color: var(--primary-neon);
            box-shadow: var(--glow-primary);
            transform: translateY(-3px);
        }

        .action-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }

        .loading {
            text-align: center;
            color: white;
            font-size: 1.2em;
            margin-top: 50px;
        }

        .error {
            text-align: center;
            color: #ffcdd2;
            background: rgba(244, 67, 54, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin-top: 50px;
        }

        .stats-bar {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
            display: flex;
            justify-content: center;
            gap: 50px;
            flex-wrap: wrap;
            border: 1px solid var(--glass-border);
            box-shadow: var(--shadow-medium);
        }

        .stat-item {
            text-align: center;
            color: white;
            padding: 20px 25px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 16px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.1);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }

        .stat-number {
            font-size: 2.8em;
            font-weight: 800;
            display: block;
            margin-bottom: 8px;
            background: linear-gradient(45deg, #fff, #e3f2fd);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            font-size: 0.95em;
            opacity: 0.9;
            text-transform: uppercase;
            letter-spacing: 1.2px;
            font-weight: 500;
        }

        /* Modal styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            position: relative;
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 🎨 ANIMATIONS POUR LES NOUVEAUX POP-UPS */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: scale(0.9);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes fadeOut {
            from {
                opacity: 1;
                transform: scale(1);
            }
            to {
                opacity: 0;
                transform: scale(0.9);
            }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }

        .modal-title {
            font-size: 1.5em;
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .close:hover {
            color: #333;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        .form-input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
        }

        .file-input-wrapper {
            position: relative;
            display: inline-block;
            width: 100%;
        }

        .file-input {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .file-input-display {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            border: 2px dashed #e0e0e0;
            border-radius: 8px;
            background: #f9f9f9;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .file-input-display:hover {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .file-input-display.has-file {
            border-color: #4CAF50;
            background: #f0fff0;
        }

        .modal-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
        }

        .modal-btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .modal-btn.primary {
            background: #667eea;
            color: white;
        }

        .modal-btn.primary:hover {
            background: #5a67d8;
        }

        .modal-btn.secondary {
            background: #f0f0f0;
            color: #333;
        }

        .modal-btn.secondary:hover {
            background: #e0e0e0;
        }

        .progress-container {
            display: none;
            margin-top: 20px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            text-align: center;
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.2em;
            }

            .projects-grid {
                grid-template-columns: 1fr;
            }

            .stats-bar {
                gap: 20px;
            }

            .action-btn {
                min-width: 100px;
                font-size: 0.8em;
                padding: 10px 12px;
            }

            .modal-content {
                margin: 10% auto;
                width: 95%;
                padding: 20px;
            }

            .modal-actions {
                flex-direction: column;
            }

            .modal-btn {
                width: 100%;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <div class="logo-container">
                <img src="../../images/logo1.png" alt="BIMEX Logo" class="logo-image" onerror="this.style.display='none'">
                <div class="brand-text">
                    <h1 class="hero-title">BIMEX 2.0</h1>
                    <p class="hero-subtitle">
                        Mission Control Center • Intelligence Artificielle • Analyse Avancée
                    </p>
                </div>
            </div>

            <!-- Statut système temps réel -->
            <div class="system-status">
                <div class="status-indicator">
                    <div class="status-dot"></div>
                    <span>SYSTÈME OPÉRATIONNEL</span>
                </div>
                <div class="status-indicator">
                    <i class="fas fa-brain"></i>
                    <span>IA CONNECTÉE</span>
                </div>
                <div class="status-indicator">
                    <i class="fas fa-clock"></i>
                    <span id="current-time">--:--:--</span>
                </div>
            </div>
        </div>

        <div class="stats-bar">
            <div class="stat-item">
                <span class="stat-number" id="projectCount">-</span>
                <span class="stat-label">Projets</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">AI</span>
                <span class="stat-label">Intelligence Artificielle</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">2.0</span>
                <span class="stat-label">Nouvelle Génération</span>
            </div>
        </div>

        

        <div class="add-model-section">
            <button class="add-model-btn" onclick="openAddModelModal()">
                <i class="fas fa-plus"></i>
                Ajouter un modèle IFC
            </button>
        </div>

        <div class="search-section">
            <div class="search-box">
                <i class="fas fa-search"></i>
                <input type="text" id="searchInput" placeholder="Rechercher un projet...">
            </div>
        </div>

        <div id="projectsContainer">
            <div class="loading">
                <i class="fas fa-spinner fa-spin"></i> Chargement des projets...
            </div>
        </div>
    </div>

    <!-- Modal pour ajouter un modèle -->
    <div id="addModelModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Ajouter un nouveau modèle IFC</h2>
                <span class="close" onclick="closeAddModelModal()">&times;</span>
            </div>

            <form id="addModelForm">
                <div class="form-group">
                    <label class="form-label" for="projectName">Nom du projet</label>
                    <input type="text" id="projectName" class="form-input" placeholder="Entrez le nom du projet..." required>
                </div>

                <div class="form-group">
                    <label class="form-label" for="ifcFile">Fichier IFC</label>
                    <div class="file-input-wrapper">
                        <input type="file" id="ifcFile" class="file-input" accept=".ifc" required>
                        <div class="file-input-display" id="fileInputDisplay">
                            <i class="fas fa-upload" style="margin-right: 10px; color: #667eea;"></i>
                            <span>Cliquez pour sélectionner un fichier IFC</span>
                        </div>
                    </div>
                </div>

                <div class="progress-container" id="progressContainer">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">Préparation...</div>
                </div>

                <div class="modal-actions">
                    <button type="button" class="modal-btn secondary" onclick="closeAddModelModal()">Annuler</button>
                    <button type="submit" class="modal-btn primary" id="submitBtn">Ajouter le modèle</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 🚀 BUSINESS INTELLIGENCE DASHBOARD RÉVOLUTIONNAIRE -->
    <div id="biDashboard" class="bi-dashboard">
        <div class="bi-dashboard-content">
           

            <!-- 📊 WIDGETS BI PRINCIPAUX -->
            <div class="bi-widgets-grid">
                <!-- Widget Apache Superset -->
                <div class="bi-widget" onclick="openSupersetWidget()">
                    <div class="bi-status-indicator"></div>
                    <div class="bi-widget-header">
                        <div class="bi-widget-icon" style="background: linear-gradient(135deg, #20a7c9 0%, #1a8aa8 100%);">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <div>
                            <div class="bi-widget-title">Apache Superset</div>
                            <div class="bi-widget-subtitle">Dashboards open-source • Analytics avancés</div>
                        </div>
                    </div>
                    <div class="bi-widget-content">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
                            <span style="color: var(--text-secondary);">Rapports actifs:</span>
                            <span style="color: var(--bi-success); font-weight: 600;">12</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
                            <span style="color: var(--text-secondary);">Dernière sync:</span>
                            <span style="color: var(--text-primary);" id="powerbiLastSync">Il y a 2 min</span>
                        </div>
                        <div style="background: rgba(242, 200, 17, 0.1); padding: 10px; border-radius: 8px; border-left: 3px solid #f2c811;">
                            <small style="color: var(--text-secondary);">🔄 Synchronisation automatique active</small>
                        </div>
                    </div>
                </div>

                <!-- Widget Tableau -->
                <div class="bi-widget" onclick="openTableauWidget()">
                    <div class="bi-status-indicator"></div>
                    <div class="bi-widget-header">
                        <div class="bi-widget-icon" style="background: linear-gradient(135deg, #1f77b4 0%, #ff7f0e 100%);">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <div>
                            <div class="bi-widget-title">Tableau Analytics</div>
                            <div class="bi-widget-subtitle">Visualisations avancées • KPIs dynamiques</div>
                        </div>
                    </div>
                    <div class="bi-widget-content">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
                            <span style="color: var(--text-secondary);">Workbooks:</span>
                            <span style="color: var(--bi-success); font-weight: 600;">8</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
                            <span style="color: var(--text-secondary);">Vues actives:</span>
                            <span style="color: var(--text-primary);">24</span>
                        </div>
                        <div style="background: rgba(31, 119, 180, 0.1); padding: 10px; border-radius: 8px; border-left: 3px solid #1f77b4;">
                            <small style="color: var(--text-secondary);">📊 Analyse prédictive en cours</small>
                        </div>
                    </div>
                </div>

                <!-- Widget n8n Workflows -->
                <div class="bi-widget" onclick="openN8nWidget()">
                    <div class="bi-status-indicator"></div>
                    <div class="bi-widget-header">
                        <div class="bi-widget-icon" style="background: linear-gradient(135deg, #ea4b71 0%, #ff6b6b 100%);">
                            <i class="fas fa-project-diagram"></i>
                        </div>
                        <div>
                            <div class="bi-widget-title">n8n Automation</div>
                            <div class="bi-widget-subtitle">Workflows intelligents • Intégrations API</div>
                        </div>
                    </div>
                    <div class="bi-widget-content">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
                            <span style="color: var(--text-secondary);">Workflows actifs:</span>
                            <span style="color: var(--bi-success); font-weight: 600;">15</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
                            <span style="color: var(--text-secondary);">Exécutions/jour:</span>
                            <span style="color: var(--text-primary);">1,247</span>
                        </div>
                        <div style="background: rgba(234, 75, 113, 0.1); padding: 10px; border-radius: 8px; border-left: 3px solid #ea4b71;">
                            <small style="color: var(--text-secondary);">⚡ Auto-export vers BI activé</small>
                        </div>
                    </div>
                </div>

                <!-- Widget ERP Integration -->
                <div class="bi-widget" onclick="openERPWidget()">
                    <div class="bi-status-indicator"></div>
                    <div class="bi-widget-header">
                        <div class="bi-widget-icon" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                            <i class="fas fa-database"></i>
                        </div>
                        <div>
                            <div class="bi-widget-title">ERP Connector</div>
                            <div class="bi-widget-subtitle">SAP • Oracle • Dynamics • Temps réel</div>
                        </div>
                    </div>
                    <div class="bi-widget-content">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
                            <span style="color: var(--text-secondary);">Systèmes connectés:</span>
                            <span style="color: var(--bi-success); font-weight: 600;">3</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
                            <span style="color: var(--text-secondary);">Sync temps réel:</span>
                            <span style="color: var(--bi-success);">✓ Active</span>
                        </div>
                        <div style="background: rgba(40, 167, 69, 0.1); padding: 10px; border-radius: 8px; border-left: 3px solid #28a745;">
                            <small style="color: var(--text-secondary);">🔗 Données financières synchronisées</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 🔗 CONNECTEURS TEMPS RÉEL -->
            <div class="bi-connectors">
                <div class="bi-connector">
                    <div class="bi-connector-header">
                        <div class="bi-connector-logo powerbi">
                            <i class="fab fa-microsoft"></i>
                        </div>
                        <div>
                            <div class="bi-connector-name">Power BI Service</div>
                            <div class="bi-connector-status">
                                <div style="width: 8px; height: 8px; background: var(--bi-success); border-radius: 50%; animation: statusBlink 2s infinite;"></div>
                                Connecté
                            </div>
                        </div>
                    </div>
                    <div class="bi-connector-actions">
                        <button class="bi-action-btn" onclick="syncPowerBI()">Synchroniser</button>
                        <button class="bi-action-btn" onclick="configurePowerBI()">Configurer</button>
                    </div>
                </div>

                <div class="bi-connector">
                    <div class="bi-connector-header">
                        <div class="bi-connector-logo tableau">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div>
                            <div class="bi-connector-name">Tableau Server</div>
                            <div class="bi-connector-status">
                                <div style="width: 8px; height: 8px; background: var(--bi-success); border-radius: 50%; animation: statusBlink 2s infinite;"></div>
                                Connecté
                            </div>
                        </div>
                    </div>
                    <div class="bi-connector-actions">
                        <button class="bi-action-btn" onclick="syncTableau()">Publier</button>
                        <button class="bi-action-btn" onclick="configureTableau()">Configurer</button>
                    </div>
                </div>

                <div class="bi-connector">
                    <div class="bi-connector-header">
                        <div class="bi-connector-logo n8n">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <div>
                            <div class="bi-connector-name">n8n Workflows</div>
                            <div class="bi-connector-status">
                                <div style="width: 8px; height: 8px; background: var(--bi-success); border-radius: 50%; animation: statusBlink 2s infinite;"></div>
                                15 workflows actifs
                            </div>
                        </div>
                    </div>
                    <div class="bi-connector-actions">
                        <button class="bi-action-btn" onclick="manageWorkflows()">Gérer</button>
                        <button class="bi-action-btn" onclick="createWorkflow()">Nouveau</button>
                    </div>
                </div>

                <div class="bi-connector">
                    <div class="bi-connector-header">
                        <div class="bi-connector-logo erp">
                            <i class="fas fa-building"></i>
                        </div>
                        <div>
                            <div class="bi-connector-name">ERP Systems</div>
                            <div class="bi-connector-status">
                                <div style="width: 8px; height: 8px; background: var(--bi-success); border-radius: 50%; animation: statusBlink 2s infinite;"></div>
                                SAP, Oracle connectés
                            </div>
                        </div>
                    </div>
                    <div class="bi-connector-actions">
                        <button class="bi-action-btn" onclick="syncERP()">Synchroniser</button>
                        <button class="bi-action-btn" onclick="configureERP()">Configurer</button>
                    </div>
                </div>
            </div>

            <!-- 🚀 ACTIONS RAPIDES BI -->
            <div style="text-align: center; margin-top: 40px; padding: 30px; background: rgba(26, 26, 46, 0.6); border-radius: 15px; border: 1px solid rgba(99, 102, 241, 0.3);">
                <h3 style="color: var(--text-primary); margin-bottom: 20px;">🚀 Actions Rapides</h3>
                <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                    <button onclick="exportToAllPlatforms()" style="
                        background: var(--bi-gradient);
                        border: none;
                        color: white;
                        padding: 15px 30px;
                        border-radius: 25px;
                        cursor: pointer;
                        font-weight: 600;
                        font-size: 16px;
                        transition: all 0.3s ease;
                        box-shadow: 0 5px 15px rgba(99, 102, 241, 0.3);
                    " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 25px rgba(99, 102, 241, 0.5)'"
                       onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 5px 15px rgba(99, 102, 241, 0.3)'">
                        <i class="fas fa-rocket"></i> Export Multi-Plateformes
                    </button>

                    <button onclick="loadBIHistory()" style="
                        background: rgba(99, 102, 241, 0.2);
                        border: 1px solid var(--bi-primary);
                        color: var(--bi-primary);
                        padding: 15px 30px;
                        border-radius: 25px;
                        cursor: pointer;
                        font-weight: 600;
                        font-size: 16px;
                        transition: all 0.3s ease;
                    " onmouseover="this.style.background='rgba(99, 102, 241, 0.3)'"
                       onmouseout="this.style.background='rgba(99, 102, 241, 0.2)'">
                        <i class="fas fa-history"></i> Historique
                    </button>

                    <button onclick="checkBIStatus()" style="
                        background: rgba(16, 185, 129, 0.2);
                        border: 1px solid var(--bi-success);
                        color: var(--bi-success);
                        padding: 15px 30px;
                        border-radius: 25px;
                        cursor: pointer;
                        font-weight: 600;
                        font-size: 16px;
                        transition: all 0.3s ease;
                    " onmouseover="this.style.background='rgba(16, 185, 129, 0.3)'"
                       onmouseout="this.style.background='rgba(16, 185, 129, 0.2)'">
                        <i class="fas fa-check-circle"></i> Statut BI
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Icônes pour différents types de projets
        const projectIcons = {
            'Test1': 'fas fa-home',
            'Duplex': 'fas fa-building',
            'Schependomlaan': 'fas fa-city',
            'WestRiversideHospital': 'fas fa-hospital',
            'OTCConferenceCenter': 'fas fa-users',
            'HolterTower': 'fas fa-building',
            'Clinic': 'fas fa-hospital-alt',
            'MAP': 'fas fa-map',
            'MAPPointCloud': 'fas fa-cloud',
            'default': 'fas fa-cube'
        };

        let allProjects = [];

        async function loadProjects() {
            try {
                const response = await fetch('./data/projects/index.json');
                const data = await response.json();

                // Charger tous les projets du dossier
                const allProjectsResponse = await loadAllProjectsFromDirectory();
                allProjects = allProjectsResponse;

                document.getElementById('projectCount').textContent = allProjects.length;
                displayProjects(allProjects);

                // Animation du compteur
                setTimeout(() => {
                    animateNumber(document.getElementById('projectCount'), allProjects.length);
                }, 500);

            } catch (error) {
                console.error('Erreur lors du chargement des projets:', error);
                document.getElementById('projectsContainer').innerHTML = `
                    <div class="error">
                        <i class="fas fa-exclamation-triangle"></i>
                        <h3>Erreur de chargement</h3>
                        <p>Impossible de charger la liste des projets.</p>
                    </div>
                `;
            }
        }

        async function loadAllProjectsFromDirectory() {
            try {
                // Utiliser l'endpoint backend pour scanner tous les projets
                const response = await fetch(`${API_BASE_URL}/scan-projects`);
                const data = await response.json();

                return data.projects || [];
            } catch (error) {
                console.error('Erreur lors du scan des projets:', error);

                // Fallback: charger depuis index.json local
                try {
                    const fallbackResponse = await fetch('./data/projects/index.json');
                    const fallbackData = await fallbackResponse.json();
                    return fallbackData.projects || [];
                } catch (fallbackError) {
                    console.error('Erreur fallback:', fallbackError);
                    return [];
                }
            }
        }

        function displayProjects(projects) {
            const container = document.getElementById('projectsContainer');

            if (projects.length === 0) {
                container.innerHTML = `
                    <div class="error">
                        <i class="fas fa-folder-open"></i>
                        <h3>Aucun projet trouvé</h3>
                        <p>Aucun projet BIM n'est disponible pour le moment.</p>
                    </div>
                `;
                return;
            }

            const projectsGrid = document.createElement('div');
            projectsGrid.className = 'projects-grid';

            projects.forEach(project => {
                const card = createProjectCard(project);
                projectsGrid.appendChild(card);
            });

            container.innerHTML = '';
            container.appendChild(projectsGrid);
        }

        function createProjectCard(project) {
            const card = document.createElement('div');
            card.className = 'project-card';

            const icon = projectIcons[project.id] || projectIcons.default;

            card.innerHTML = `
                <div class="project-header">
                    <div class="project-icon">
                        <i class="${icon}"></i>
                    </div>
                    <div class="project-info">
                        <h3>${project.name}</h3>
                        <div class="project-id">ID: ${project.id}</div>
                    </div>
                </div>
                <div class="project-actions">
                    <!-- Action principale -->
                    <button class="action-btn primary main-action" onclick="analyzeProject('${project.id}')">
                        <i class="fas fa-rocket"></i>
                        Lancer l'Analyse IA
                    </button>

                    <!-- Menu contextuel innovant -->
                    <div class="action-menu-container">
                        <button class="action-menu-trigger" onclick="toggleActionMenu('${project.id}')">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>

                        <div class="action-menu" id="menu-${project.id}">
                            <div class="action-menu-item" onclick="viewModel('${project.id}')">
                                <i class="fas fa-eye"></i>
                                <span>Visualiser 3D</span>
                                <small>Modèle interactif</small>
                            </div>

                            <div class="action-menu-item" onclick="predictiveMaintenance('${project.id}')">
                                <i class="fas fa-tools"></i>
                                <span>Maintenance IA</span>
                                <small>Prédictions avancées</small>
                            </div>

                            <div class="action-menu-item" onclick="compareProject('${project.id}')">
                                <i class="fas fa-balance-scale"></i>
                                <span>Comparaison</span>
                                <small>Analyse comparative</small>
                            </div>

                            <div class="action-menu-item" onclick="exportProject('${project.id}')">
                                <i class="fas fa-download"></i>
                                <span>Exporter</span>
                                <small>Rapport complet</small>
                            </div>

                            <div class="action-menu-item disabled" onclick="viewBlueprint('${project.id}')">
                                <i class="fas fa-drafting-compass"></i>
                                <span>Blueprint</span>
                                <small>Bientôt disponible</small>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            return card;
        }

        // Fonctions d'action
        function viewModel(projectId) {
            // 🔧 CORRECTION: Utiliser le bon chemin pour le viewer XeoKit (depuis la racine du serveur)
            const viewerUrl = `http://localhost:8081/xeokit-bim-viewer/app/index.html?projectId=${projectId}`;
            console.log(`🔍 Ouverture du viewer pour le projet: ${projectId} -> ${viewerUrl}`);
            window.open(viewerUrl, '_blank');
        }

        function analyzeProject(projectId) {
            // 🚀 Redirection vers l'analyse automatique du projet (Backend port 8000)
            console.log(`🔍 Analyse du projet: ${projectId}`);

            // Configuration du serveur unifié (port 8081)
            const UNIFIED_SERVER_URL = 'http://localhost:8081';
            const BACKEND_URL = UNIFIED_SERVER_URL; // Même serveur maintenant
            const FRONTEND_URL = UNIFIED_SERVER_URL;

            // Afficher un message de chargement
            const loadingMsg = document.createElement('div');
            loadingMsg.style.cssText = `
                position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
                background: rgba(0,0,0,0.9); color: white; padding: 30px; border-radius: 15px;
                z-index: 10000; text-align: center; font-size: 16px; box-shadow: 0 10px 30px rgba(0,0,0,0.5);
                backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.2);
            `;
            loadingMsg.innerHTML = `
                <div style="font-size: 3rem; margin-bottom: 15px;">🤖</div>
                <div style="font-size: 1.2rem; font-weight: bold; margin-bottom: 10px;">BIMEX Analyzer</div>
                <div style="margin-bottom: 15px;">
                    <i class="fas fa-spinner fa-spin" style="font-size: 1.5rem;"></i>
                </div>
                <div>Analyse du projet <strong>${projectId}</strong> en cours...</div>
                <div style="font-size: 0.9rem; opacity: 0.8; margin-top: 10px;">
                    Analyse sur serveur unifié...
                </div>
                <div style="margin-top: 15px; font-size: 0.8rem; opacity: 0.6;">
                    Serveur unifié: ${UNIFIED_SERVER_URL}
                </div>
            `;
            document.body.appendChild(loadingMsg);

            // Vérifier si le backend est disponible et rediriger automatiquement
            setTimeout(async () => {
                try {
                    // Tester si le backend est disponible
                    const response = await fetch('http://localhost:8001/health', {
                        method: 'GET',
                        mode: 'cors'
                    });

                    if (response.ok) {
                        // Backend disponible - Redirection automatique
                        document.body.removeChild(loadingMsg);

                        const successMsg = document.createElement('div');
                        successMsg.style.cssText = loadingMsg.style.cssText;
                        successMsg.innerHTML = `
                            <div style="font-size: 3rem; margin-bottom: 15px;">✅</div>
                            <div style="font-size: 1.2rem; font-weight: bold; margin-bottom: 10px;">Backend Détecté !</div>
                            <div style="margin-bottom: 15px;">
                                <i class="fas fa-rocket" style="font-size: 1.5rem;"></i>
                            </div>
                            <div>Redirection vers l'analyse du projet <strong>${projectId}</strong>...</div>
                            <div style="font-size: 0.9rem; opacity: 0.8; margin-top: 10px;">
                                Backend: http://localhost:8001 ✅
                            </div>
                        `;
                        document.body.appendChild(successMsg);

                        // Afficher le popup d'auto-analyse
                        setTimeout(() => {
                            showAutoAnalysisPopup(projectId);
                        }, 1500);

                    } else {
                        throw new Error('Backend non disponible');
                    }
                } catch (error) {
                    // Backend non disponible - Afficher les instructions
                    document.body.removeChild(loadingMsg);

                    alert(`🎯 Projet sélectionné: ${projectId}

⚠️ Backend d'analyse non détecté sur le port 8001

📋 Pour démarrer l'analyse:
1. Ouvrez un terminal dans le dossier backend
2. Exécutez: python -m uvicorn main:app --host 0.0.0.0 --port 8001 --reload
3. Puis cliquez à nouveau sur "Analyser Fichier"

🔗 Ou utilisez directement: http://localhost:8001/analyze-project/${projectId}

💡 Le backend doit être démarré pour l'analyse automatique !`);
                }
            }, 2000);
        }

        function viewBlueprint(projectId) {
            // Fonctionnalité à implémenter plus tard
            alert(`Fonctionnalité "Voir blueprint" pour ${projectId} - À venir prochainement !`);
        }

        // Animation des statistiques
        function animateNumber(element, target) {
            let current = 0;
            const increment = target / 50;
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(current);
            }, 30);
        }

        // Fonction de recherche
        function setupSearch() {
            const searchInput = document.getElementById('searchInput');
            searchInput.addEventListener('input', (e) => {
                const searchTerm = e.target.value.toLowerCase();
                const filteredProjects = allProjects.filter(project =>
                    project.name.toLowerCase().includes(searchTerm) ||
                    project.id.toLowerCase().includes(searchTerm)
                );
                displayProjects(filteredProjects);
            });
        }

        // Configuration de l'API backend
        const API_BASE_URL = 'http://localhost:8001';

        // Fonctions pour le modal d'ajout de modèle
        function openAddModelModal() {
            document.getElementById('addModelModal').style.display = 'block';
            document.getElementById('projectName').focus();
        }

        function closeAddModelModal() {
            const modal = document.getElementById('addModelModal');
            modal.style.display = 'none';

            // Réinitialiser le formulaire
            document.getElementById('addModelForm').reset();
            document.getElementById('fileInputDisplay').innerHTML = `
                <i class="fas fa-upload" style="margin-right: 10px; color: #667eea;"></i>
                <span>Cliquez pour sélectionner un fichier IFC</span>
            `;
            document.getElementById('fileInputDisplay').classList.remove('has-file');
            document.getElementById('progressContainer').style.display = 'none';
            document.getElementById('submitBtn').disabled = false;
        }

        // Gestion du sélecteur de fichier
        document.addEventListener('DOMContentLoaded', function() {
            const fileInput = document.getElementById('ifcFile');
            const fileDisplay = document.getElementById('fileInputDisplay');

            fileInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    fileDisplay.innerHTML = `
                        <i class="fas fa-file" style="margin-right: 10px; color: #4CAF50;"></i>
                        <span>${file.name}</span>
                    `;
                    fileDisplay.classList.add('has-file');
                } else {
                    fileDisplay.innerHTML = `
                        <i class="fas fa-upload" style="margin-right: 10px; color: #667eea;"></i>
                        <span>Cliquez pour sélectionner un fichier IFC</span>
                    `;
                    fileDisplay.classList.remove('has-file');
                }
            });
        });

        // Gestion de la soumission du formulaire
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('addModelForm');
            form.addEventListener('submit', async function(e) {
                e.preventDefault();
                await handleModelUpload();
            });
        });

        async function handleModelUpload() {
            const projectName = document.getElementById('projectName').value.trim();
            const fileInput = document.getElementById('ifcFile');
            const file = fileInput.files[0];

            if (!projectName || !file) {
                alert('Veuillez remplir tous les champs');
                return;
            }

            // Désactiver le bouton de soumission
            const submitBtn = document.getElementById('submitBtn');
            submitBtn.disabled = true;
            submitBtn.textContent = 'Traitement...';

            // Afficher la barre de progression
            const progressContainer = document.getElementById('progressContainer');
            progressContainer.style.display = 'block';

            try {
                // Préparer les données du formulaire
                const formData = new FormData();
                formData.append('file', file);
                formData.append('project_name', projectName);

                updateProgress(0, 'Envoi du fichier...');

                // Envoyer le fichier au backend
                const response = await fetch(`${API_BASE_URL}/upload-ifc`, {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || 'Erreur lors de l\'upload');
                }

                const result = await response.json();
                const conversionId = result.conversion_id;

                updateProgress(10, 'Fichier envoyé, conversion en cours...');

                // Suivre le progrès de la conversion
                await trackConversionProgress(conversionId);

            } catch (error) {
                console.error('Erreur:', error);
                updateProgress(0, `Erreur: ${error.message}`);
                submitBtn.disabled = false;
                submitBtn.textContent = 'Ajouter le modèle';

                setTimeout(() => {
                    progressContainer.style.display = 'none';
                }, 3000);
            }
        }

        async function trackConversionProgress(conversionId) {
            const checkProgress = async () => {
                try {
                    const response = await fetch(`${API_BASE_URL}/conversion-status/${conversionId}`);
                    if (!response.ok) {
                        throw new Error('Erreur lors de la vérification du statut');
                    }

                    const status = await response.json();
                    updateProgress(status.progress, status.message);

                    if (status.status === 'completed') {
                        updateProgress(100, 'Conversion terminée avec succès!');

                        // Recharger la liste des projets
                        setTimeout(async () => {
                            await loadProjects();
                            closeAddModelModal();
                        }, 1500);

                        return;
                    } else if (status.status === 'failed') {
                        throw new Error(status.message);
                    }

                    // Continuer à vérifier si la conversion est en cours (sans limite de temps)
                    if (status.status === 'processing') {
                        setTimeout(checkProgress, 1000); // Vérifier toutes les secondes
                    }

                } catch (error) {
                    console.error('Erreur lors du suivi:', error);
                    updateProgress(0, `Erreur: ${error.message}`);

                    const submitBtn = document.getElementById('submitBtn');
                    submitBtn.disabled = false;
                    submitBtn.textContent = 'Ajouter le modèle';
                }
            };

            checkProgress();
        }

        function updateProgress(percentage, message) {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');

            progressFill.style.width = `${percentage}%`;
            progressText.textContent = message;
        }

        // Fermer le modal en cliquant à l'extérieur
        window.addEventListener('click', function(event) {
            const modal = document.getElementById('addModelModal');
            if (event.target === modal) {
                closeAddModelModal();
            }
        });

        function showAutoAnalysisPopup(projectId) {
            // Créer le popup d'auto-analyse
            const popup = document.createElement('div');
            popup.id = 'autoAnalysisPopup';
            popup.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                animation: fadeIn 0.3s ease;
            `;

            popup.innerHTML = `
                <div style="
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    padding: 40px;
                    border-radius: 20px;
                    max-width: 600px;
                    width: 90%;
                    text-align: center;
                    color: white;
                    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                    animation: slideIn 0.5s ease;
                ">
                    <h2 style="margin-bottom: 20px; font-size: 2rem;">🤖 BIMEX Auto-Analyse</h2>
                    <p style="margin-bottom: 30px; font-size: 1.2rem;">Analyse automatique du projet <strong>${projectId}</strong></p>

                    <div id="analysisSteps" style="margin: 30px 0;">
                        <div class="analysis-step" data-step="1">📁 Détection du fichier...</div>
                        <div class="analysis-step" data-step="2">🔍 Analyse des données...</div>
                        <div class="analysis-step" data-step="3">🚨 Détection d'anomalies...</div>
                        <div class="analysis-step" data-step="4">🏢 Classification IA...</div>
                        <div class="analysis-step" data-step="5">♿ Analyse PMR...</div>
                        <div class="analysis-step" data-step="6">📄 Préparation du rapport...</div>
                    </div>

                    <div id="progressBar" style="
                        width: 100%;
                        height: 8px;
                        background: rgba(255,255,255,0.3);
                        border-radius: 4px;
                        overflow: hidden;
                        margin: 20px 0;
                    ">
                        <div id="progress" style="
                            width: 0%;
                            height: 100%;
                            background: linear-gradient(90deg, #10b981, #34d399);
                            transition: width 0.5s ease;
                        "></div>
                    </div>

                    <p id="statusText" style="margin-top: 20px; font-size: 1rem; opacity: 0.9;">Initialisation...</p>
                </div>
            `;

            // Ajouter les styles CSS pour les animations
            const style = document.createElement('style');
            style.textContent = `
                @keyframes fadeIn {
                    from { opacity: 0; }
                    to { opacity: 1; }
                }
                @keyframes slideIn {
                    from { transform: translateY(-50px); opacity: 0; }
                    to { transform: translateY(0); opacity: 1; }
                }
                .analysis-step {
                    padding: 8px 0;
                    opacity: 0.5;
                    transition: all 0.3s ease;
                    font-size: 1.1rem;
                }
                .analysis-step.active {
                    opacity: 1;
                    transform: scale(1.05);
                    color: #34d399;
                    font-weight: bold;
                }
                .analysis-step.completed {
                    opacity: 0.8;
                    color: #10b981;
                }
            `;
            document.head.appendChild(style);
            document.body.appendChild(popup);

            // Démarrer l'animation d'analyse
            startAutoAnalysisAnimation(projectId);
        }

        async function startAutoAnalysisAnimation(projectId) {
            const steps = document.querySelectorAll('.analysis-step');
            const progressBar = document.getElementById('progress');
            const statusText = document.getElementById('statusText');

            const statusTexts = [
                'Détection du fichier géométrie...',
                'Extraction des éléments BIM...',
                'Recherche d\'anomalies...',
                'Classification par intelligence artificielle...',
                'Vérification de l\'accessibilité PMR...',
                'Finalisation du rapport d\'analyse...'
            ];

            for (let i = 0; i < steps.length; i++) {
                // Activer l'étape courante
                steps[i].classList.add('active');
                statusText.textContent = statusTexts[i];

                // Mettre à jour la barre de progression
                const progress = ((i + 1) / steps.length) * 100;
                progressBar.style.width = progress + '%';

                // Attendre 1.5 secondes
                await new Promise(resolve => setTimeout(resolve, 1500));

                // Marquer comme terminé
                steps[i].classList.remove('active');
                steps[i].classList.add('completed');
            }

            // Animation de fin
            statusText.textContent = '✅ Analyse terminée ! Redirection vers l\'interface d\'analyse...';

            // Attendre 2 secondes puis rediriger
            setTimeout(() => {
                // Fermer le popup
                document.getElementById('autoAnalysisPopup').remove();

                // Nettoyer le sessionStorage pour forcer la reconfiguration
                sessionStorage.removeItem('autoModeConfigured');
                sessionStorage.clear();

                // Rediriger vers la nouvelle interface BIMEX 2.0 avec détection automatique
                window.location.href = `../../frontend/bim_analysis.html?project=${projectId}&auto=true&file_detected=true&step=detailed`;
            }, 2000);
        }

        // 🚀 NOUVELLES FONCTIONNALITÉS DATA SCIENCE

        // Maintenance Prédictive
        function predictiveMaintenance(projectId) {
            console.log(`🔧 Maintenance prédictive pour le projet: ${projectId}`);

            // Créer un pop-up moderne pour la maintenance prédictive
            const popup = document.createElement('div');
            popup.id = 'maintenancePopup';
            popup.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0, 0, 0, 0.8); backdrop-filter: blur(10px);
                display: flex; justify-content: center; align-items: center; z-index: 10000;
                animation: fadeIn 0.3s ease;
            `;

            popup.innerHTML = `
                <div style="
                    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
                    border-radius: 20px; padding: 40px; max-width: 800px; width: 90%;
                    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
                    border: 1px solid rgba(255, 255, 255, 0.2);
                ">
                    <div style="
                        background: var(--success-gradient); color: white;
                        padding: 20px; margin: -40px -40px 30px -40px;
                        border-radius: 20px 20px 0 0;
                        display: flex; justify-content: space-between; align-items: center;
                    ">
                        <h2 style="margin: 0; display: flex; align-items: center; gap: 15px;">
                            <i class="fas fa-tools"></i>
                            Maintenance Prédictive - ${projectId}
                        </h2>
                        <button onclick="closeMaintenancePopup()" style="
                            background: rgba(255, 255, 255, 0.2); border: none; color: white;
                            width: 40px; height: 40px; border-radius: 50%; cursor: pointer;
                            display: flex; align-items: center; justify-content: center;
                        ">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <div class="maintenance-content">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
                            <div style="background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%); padding: 20px; border-radius: 12px; text-align: center; border: 1px solid #fecaca;">
                                <div style="font-size: 2em; font-weight: bold; color: #dc2626; margin-bottom: 5px;">3</div>
                                <div style="color: #991b1b; font-weight: 500;">Éléments Critiques</div>
                            </div>
                            <div style="background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%); padding: 20px; border-radius: 12px; text-align: center; border: 1px solid #fbbf24;">
                                <div style="font-size: 2em; font-weight: bold; color: #d97706; margin-bottom: 5px;">8</div>
                                <div style="color: #92400e; font-weight: 500;">Maintenance Préventive</div>
                            </div>
                            <div style="background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%); padding: 20px; border-radius: 12px; text-align: center; border: 1px solid #34d399;">
                                <div style="font-size: 2em; font-weight: bold; color: #059669; margin-bottom: 5px;">45</div>
                                <div style="color: #065f46; font-weight: 500;">Éléments Sains</div>
                            </div>
                            <div style="background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%); padding: 20px; border-radius: 12px; text-align: center; border: 1px solid #60a5fa;">
                                <div style="font-size: 2em; font-weight: bold; color: #2563eb; margin-bottom: 5px;">€15,000</div>
                                <div style="color: #1e40af; font-weight: 500;">Coût Prévu/An</div>
                            </div>
                        </div>

                        <div style="background: #f8fafc; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                            <h4 style="color: #374151; margin-bottom: 15px;">📅 Planning de Maintenance Prédictive</h4>
                            <div style="display: flex; flex-direction: column; gap: 10px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px; background: white; border-radius: 8px; border-left: 4px solid #dc2626;">
                                    <span><strong>Système HVAC</strong> - Inspection critique</span>
                                    <span style="background: #fecaca; color: #991b1b; padding: 4px 12px; border-radius: 12px; font-size: 0.9em;">Dans 2 semaines</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px; background: white; border-radius: 8px; border-left: 4px solid #f59e0b;">
                                    <span><strong>Fenêtres Étage 3</strong> - Maintenance préventive</span>
                                    <span style="background: #fde68a; color: #92400e; padding: 4px 12px; border-radius: 12px; font-size: 0.9em;">Dans 1 mois</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px; background: white; border-radius: 8px; border-left: 4px solid #10b981;">
                                    <span><strong>Structure Béton</strong> - Contrôle de routine</span>
                                    <span style="background: #a7f3d0; color: #065f46; padding: 4px 12px; border-radius: 12px; font-size: 0.9em;">Dans 3 mois</span>
                                </div>
                            </div>
                        </div>

                        <div style="display: flex; gap: 15px; justify-content: flex-end;">
                            <button onclick="closeMaintenancePopup()" style="
                                background: #f1f5f9; color: #64748b; border: none;
                                padding: 12px 24px; border-radius: 8px; cursor: pointer;
                                font-weight: 500; transition: all 0.3s ease;
                            ">
                                Fermer
                            </button>
                            <button onclick="exportMaintenanceReport('${projectId}')" style="
                                background: var(--success-gradient); color: white; border: none;
                                padding: 12px 24px; border-radius: 8px; cursor: pointer;
                                font-weight: 500; transition: all 0.3s ease;
                                display: flex; align-items: center; gap: 8px;
                            ">
                                <i class="fas fa-download"></i>
                                Exporter Rapport
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(popup);
        }

        // Comparaison de Projets
        function compareProject(projectId) {
            console.log(`📊 Comparaison pour le projet: ${projectId}`);

            // Créer un pop-up moderne pour la comparaison
            const popup = document.createElement('div');
            popup.id = 'comparePopup';
            popup.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0, 0, 0, 0.8); backdrop-filter: blur(10px);
                display: flex; justify-content: center; align-items: center; z-index: 10000;
                animation: fadeIn 0.3s ease;
            `;

            popup.innerHTML = `
                <div style="
                    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
                    border-radius: 20px; padding: 40px; max-width: 1000px; width: 95%;
                    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    max-height: 90vh; overflow-y: auto;
                ">
                    <div style="
                        background: var(--warning-gradient); color: white;
                        padding: 20px; margin: -40px -40px 30px -40px;
                        border-radius: 20px 20px 0 0;
                        display: flex; justify-content: space-between; align-items: center;
                    ">
                        <h2 style="margin: 0; display: flex; align-items: center; gap: 15px;">
                            <i class="fas fa-balance-scale"></i>
                            Comparaison de Projets - ${projectId}
                        </h2>
                        <button onclick="closeComparePopup()" style="
                            background: rgba(255, 255, 255, 0.2); border: none; color: white;
                            width: 40px; height: 40px; border-radius: 50%; cursor: pointer;
                            display: flex; align-items: center; justify-content: center;
                        ">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <div class="compare-content">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px;">
                            <div style="background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%); padding: 20px; border-radius: 12px; text-align: center; border: 1px solid #7dd3fc;">
                                <div style="font-size: 1.8em; font-weight: bold; color: #0369a1; margin-bottom: 5px;">85%</div>
                                <div style="color: #0c4a6e; font-weight: 500;">Performance Globale</div>
                                <div style="font-size: 0.9em; color: #0369a1; margin-top: 5px;">↑ 12% vs moyenne</div>
                            </div>
                            <div style="background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%); padding: 20px; border-radius: 12px; text-align: center; border: 1px solid #86efac;">
                                <div style="font-size: 1.8em; font-weight: bold; color: #16a34a; margin-bottom: 5px;">A+</div>
                                <div style="color: #15803d; font-weight: 500;">Efficacité Énergétique</div>
                                <div style="font-size: 0.9em; color: #16a34a; margin-top: 5px;">Top 10% industrie</div>
                            </div>
                            <div style="background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%); padding: 20px; border-radius: 12px; text-align: center; border: 1px solid #fbbf24;">
                                <div style="font-size: 1.8em; font-weight: bold; color: #d97706; margin-bottom: 5px;">€1,450</div>
                                <div style="color: #92400e; font-weight: 500;">Coût/m² vs €1,600</div>
                                <div style="font-size: 0.9em; color: #d97706; margin-top: 5px;">↓ 9% économie</div>
                            </div>
                            <div style="background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%); padding: 20px; border-radius: 12px; text-align: center; border: 1px solid #c084fc;">
                                <div style="font-size: 1.8em; font-weight: bold; color: #9333ea; margin-bottom: 5px;">92%</div>
                                <div style="color: #7c3aed; font-weight: 500;">Score Qualité BIM</div>
                                <div style="font-size: 0.9em; color: #9333ea; margin-top: 5px;">Excellent niveau</div>
                            </div>
                        </div>

                        <div style="background: #f8fafc; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                            <h4 style="color: #374151; margin-bottom: 15px;">📊 Benchmarks Industrie</h4>
                            <div style="display: flex; flex-direction: column; gap: 15px;">
                                <div>
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                        <span style="font-weight: 500;">Durabilité Environnementale</span>
                                        <span style="color: #16a34a; font-weight: bold;">78% (↑ Excellent)</span>
                                    </div>
                                    <div style="background: #e5e7eb; height: 8px; border-radius: 4px; overflow: hidden;">
                                        <div style="background: linear-gradient(90deg, #16a34a, #22c55e); height: 100%; width: 78%; border-radius: 4px;"></div>
                                    </div>
                                </div>
                                <div>
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                        <span style="font-weight: 500;">Accessibilité PMR</span>
                                        <span style="color: #059669; font-weight: bold;">95% (↑ Excellent)</span>
                                    </div>
                                    <div style="background: #e5e7eb; height: 8px; border-radius: 4px; overflow: hidden;">
                                        <div style="background: linear-gradient(90deg, #059669, #10b981); height: 100%; width: 95%; border-radius: 4px;"></div>
                                    </div>
                                </div>
                                <div>
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                        <span style="font-weight: 500;">Innovation Technologique</span>
                                        <span style="color: #2563eb; font-weight: bold;">82% (↑ Très bon)</span>
                                    </div>
                                    <div style="background: #e5e7eb; height: 8px; border-radius: 4px; overflow: hidden;">
                                        <div style="background: linear-gradient(90deg, #2563eb, #3b82f6); height: 100%; width: 82%; border-radius: 4px;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div style="background: #f0f9ff; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                            <h4 style="color: #1e40af; margin-bottom: 15px;">🏆 Points Forts Identifiés</h4>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
                                <div style="background: white; padding: 15px; border-radius: 8px; border-left: 4px solid #10b981;">
                                    <strong style="color: #059669;">✅ Efficacité Énergétique</strong>
                                    <p style="margin: 5px 0 0 0; font-size: 0.9em; color: #374151;">Performance supérieure de 15% à la moyenne</p>
                                </div>
                                <div style="background: white; padding: 15px; border-radius: 8px; border-left: 4px solid #3b82f6;">
                                    <strong style="color: #2563eb;">✅ Qualité BIM</strong>
                                    <p style="margin: 5px 0 0 0; font-size: 0.9em; color: #374151;">Modèle très bien structuré et détaillé</p>
                                </div>
                                <div style="background: white; padding: 15px; border-radius: 8px; border-left: 4px solid #f59e0b;">
                                    <strong style="color: #d97706;">✅ Optimisation Coûts</strong>
                                    <p style="margin: 5px 0 0 0; font-size: 0.9em; color: #374151;">Économies significatives réalisées</p>
                                </div>
                            </div>
                        </div>

                        <div style="display: flex; gap: 15px; justify-content: flex-end;">
                            <button onclick="closeComparePopup()" style="
                                background: #f1f5f9; color: #64748b; border: none;
                                padding: 12px 24px; border-radius: 8px; cursor: pointer;
                                font-weight: 500; transition: all 0.3s ease;
                            ">
                                Fermer
                            </button>
                            <button onclick="exportComparisonReport('${projectId}')" style="
                                background: var(--warning-gradient); color: white; border: none;
                                padding: 12px 24px; border-radius: 8px; cursor: pointer;
                                font-weight: 500; transition: all 0.3s ease;
                                display: flex; align-items: center; gap: 8px;
                            ">
                                <i class="fas fa-download"></i>
                                Exporter Comparaison
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(popup);
        }

        // Gestion du menu contextuel innovant
        function toggleActionMenu(projectId) {
            const menu = document.getElementById(`menu-${projectId}`);
            const allMenus = document.querySelectorAll('.action-menu');

            // Fermer tous les autres menus
            allMenus.forEach(m => {
                if (m !== menu) {
                    m.classList.remove('active');
                }
            });

            // Toggle le menu actuel
            menu.classList.toggle('active');
        }

        // Fermer les menus en cliquant ailleurs
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.action-menu-container')) {
                document.querySelectorAll('.action-menu').forEach(menu => {
                    menu.classList.remove('active');
                });
            }
        });

        // Nouvelle fonction d'export
        function exportProject(projectId) {
            console.log(`📄 Export du projet ${projectId}`);

            // Animation de feedback
            const trigger = event.target.closest('.action-menu-item');
            if (trigger) {
                trigger.style.background = 'rgba(0, 255, 136, 0.2)';
                setTimeout(() => {
                    trigger.style.background = '';
                }, 300);
            }

            // Simuler l'export
            setTimeout(() => {
                alert(`✅ Rapport du projet ${projectId} exporté avec succès !`);
            }, 1000);

            // Fermer le menu
            const menu = document.getElementById(`menu-${projectId}`);
            if (menu) menu.classList.remove('active');
        }

        // Fonctions utilitaires pour fermer les pop-ups
        function closeMaintenancePopup() {
            const popup = document.getElementById('maintenancePopup');
            if (popup) {
                popup.style.animation = 'fadeOut 0.3s ease';
                setTimeout(() => popup.remove(), 300);
            }
        }

        function closeComparePopup() {
            const popup = document.getElementById('comparePopup');
            if (popup) {
                popup.style.animation = 'fadeOut 0.3s ease';
                setTimeout(() => popup.remove(), 300);
            }
        }

        // Fonctions d'export
        function exportMaintenanceReport(projectId) {
            console.log(`📄 Export rapport maintenance pour ${projectId}`);
            alert(`Rapport de maintenance prédictive pour ${projectId} exporté !`);
        }

        function exportComparisonReport(projectId) {
            console.log(`📄 Export rapport comparaison pour ${projectId}`);
            alert(`Rapport de comparaison pour ${projectId} exporté !`);
        }

        // Horloge temps réel pour le statut système
        function updateTime() {
            const now = new Date();
            const timeElement = document.getElementById('current-time');
            if (timeElement) {
                timeElement.textContent = now.toLocaleTimeString('fr-FR');
            }
        }

        // Mettre à jour l'horloge toutes les secondes
        setInterval(updateTime, 1000);

        // Initialisation
        window.onload = function() {
            loadProjects();
            setupSearch();
            updateTime(); // Initialiser l'horloge immédiatement
        };

        // 🚀 BUSINESS INTELLIGENCE FUNCTIONS RÉVOLUTIONNAIRES
        let biDashboardActive = false;
        let biUpdateInterval;
        let biAnimationFrame;

        

        function startBIAnimations() {
            // Animation des icônes des widgets
            document.querySelectorAll('.bi-widget-icon').forEach((icon, index) => {
                icon.style.animation = `iconRotate 4s linear infinite`;
                icon.style.animationDelay = `${index * 0.5}s`;
            });

            // Animation des indicateurs de statut
            document.querySelectorAll('.bi-status-indicator').forEach(indicator => {
                indicator.style.animation = 'statusBlink 2s infinite';
            });
        }

        function stopBIAnimations() {
            if (biUpdateInterval) {
                clearInterval(biUpdateInterval);
            }
            if (biAnimationFrame) {
                cancelAnimationFrame(biAnimationFrame);
            }
        }

        function initBIRealTimeUpdates() {
            // Mise à jour des données en temps réel
            biUpdateInterval = setInterval(() => {
                updatePowerBIMetrics();
                updateTableauMetrics();
                updateN8nMetrics();
                updateERPMetrics();
                updateSyncTimestamps();
            }, 5000);
        }

        function updatePowerBIMetrics() {
            // Simulation de données Power BI en temps réel
            const reportsCount = document.querySelector('.bi-widget:nth-child(1) .bi-widget-content span:nth-child(2)');
            if (reportsCount) {
                const currentCount = parseInt(reportsCount.textContent) || 12;
                const newCount = currentCount + Math.floor(Math.random() * 3) - 1;
                reportsCount.textContent = Math.max(1, newCount);

                // Animation de mise à jour
                reportsCount.style.animation = 'dataUpdate 0.5s ease-in-out';
                setTimeout(() => {
                    reportsCount.style.animation = '';
                }, 500);
            }
        }

        function updateTableauMetrics() {
            // Simulation de données Tableau en temps réel
            const workbooksCount = document.querySelector('.bi-widget:nth-child(2) .bi-widget-content span:nth-child(2)');
            const viewsCount = document.querySelector('.bi-widget:nth-child(2) .bi-widget-content div:nth-child(2) span:nth-child(2)');

            if (workbooksCount) {
                const currentCount = parseInt(workbooksCount.textContent) || 8;
                workbooksCount.textContent = Math.max(1, currentCount + Math.floor(Math.random() * 2) - 1);
            }

            if (viewsCount) {
                const currentViews = parseInt(viewsCount.textContent) || 24;
                viewsCount.textContent = Math.max(1, currentViews + Math.floor(Math.random() * 5) - 2);
            }
        }

        function updateN8nMetrics() {
            // Simulation de données n8n en temps réel
            const workflowsCount = document.querySelector('.bi-widget:nth-child(3) .bi-widget-content span:nth-child(2)');
            const executionsCount = document.querySelector('.bi-widget:nth-child(3) .bi-widget-content div:nth-child(2) span:nth-child(2)');

            if (workflowsCount) {
                const currentCount = parseInt(workflowsCount.textContent) || 15;
                workflowsCount.textContent = Math.max(1, currentCount + Math.floor(Math.random() * 2) - 1);
            }

            if (executionsCount) {
                const currentExec = parseInt(executionsCount.textContent.replace(',', '')) || 1247;
                const newExec = currentExec + Math.floor(Math.random() * 50) + 10;
                executionsCount.textContent = newExec.toLocaleString();
            }
        }

        function updateERPMetrics() {
            // Simulation de données ERP en temps réel
            const systemsCount = document.querySelector('.bi-widget:nth-child(4) .bi-widget-content span:nth-child(2)');
            if (systemsCount) {
                // Garder stable pour les systèmes ERP
                systemsCount.textContent = '3';
            }
        }

        function updateSyncTimestamps() {
            // Mise à jour des timestamps de synchronisation
            const powerbiSync = document.getElementById('powerbiLastSync');
            if (powerbiSync) {
                const minutes = Math.floor(Math.random() * 5) + 1;
                powerbiSync.textContent = `Il y a ${minutes} min`;
            }
        }

        // 🔗 FONCTIONS D'INTÉGRATION SPÉCIFIQUES AVEC VRAIES APIs OPEN-SOURCE
        async function syncSuperset() {
            console.log('🔄 Synchronisation Apache Superset...');
            showBINotification('Superset', 'Synchronisation en cours...', 'info');

            try {
                // Obtenir le projet actuel (vous pouvez adapter selon votre logique)
                const currentProject = getCurrentProjectId();
                if (!currentProject) {
                    showBINotification('Superset', 'Aucun projet sélectionné', 'warning');
                    return;
                }

                // Appel API réel vers Superset
                const formData = new FormData();
                formData.append('project_id', currentProject);

                const response = await fetch('http://localhost:8001/bi/export-superset', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    showBINotification('Superset', `Export réussi ! ${result.data_summary.total_elements} éléments exportés`, 'success');
                    updateSupersetMetrics();

                    // Mettre à jour les métriques avec les vraies données
                    updateRealTimeMetrics('superset', result.data_summary);
                } else {
                    showBINotification('Superset', `Erreur: ${result.error || 'Export échoué'}`, 'error');
                }

            } catch (error) {
                console.error('Erreur sync Superset:', error);
                showBINotification('Superset', 'Erreur de connexion au serveur', 'error');
            }
        }

        async function syncTableau() {
            console.log('📊 Publication Tableau...');
            showBINotification('Tableau', 'Publication des workbooks...', 'info');

            try {
                const currentProject = getCurrentProjectId();
                if (!currentProject) {
                    showBINotification('Tableau', 'Aucun projet sélectionné', 'warning');
                    return;
                }

                const formData = new FormData();
                formData.append('project_id', currentProject);

                const response = await fetch('http://localhost:8001/bi/export-tableau', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    showBINotification('Tableau', `Publication réussie ! ${result.data_summary.data_points} points de données`, 'success');
                    updateTableauMetrics();
                    updateRealTimeMetrics('tableau', result.data_summary);
                } else {
                    showBINotification('Tableau', `Erreur: ${result.error || 'Publication échouée'}`, 'error');
                }

            } catch (error) {
                console.error('Erreur sync Tableau:', error);
                showBINotification('Tableau', 'Erreur de connexion au serveur', 'error');
            }
        }

        async function syncERP() {
            console.log('🏢 Synchronisation ERP...');
            showBINotification('ERP', 'Synchronisation des données financières...', 'info');

            try {
                const currentProject = getCurrentProjectId();
                if (!currentProject) {
                    showBINotification('ERP', 'Aucun projet sélectionné', 'warning');
                    return;
                }

                const formData = new FormData();
                formData.append('project_id', currentProject);

                const response = await fetch('http://localhost:8001/bi/sync-erp', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    showBINotification('ERP', `Sync réussie ! Projet ERP: ${result.erp_project_id || 'N/A'}`, 'success');
                    updateERPMetrics();
                    updateRealTimeMetrics('erp', result.synced_data);
                } else {
                    showBINotification('ERP', `Erreur: ${result.error || 'Synchronisation échouée'}`, 'error');
                }

            } catch (error) {
                console.error('Erreur sync ERP:', error);
                showBINotification('ERP', 'Erreur de connexion au serveur', 'error');
            }
        }

        async function manageWorkflows() {
            console.log('⚙️ Gestion des workflows n8n...');
            showBINotification('n8n', 'Chargement des workflows...', 'info');

            try {
                // Déclencher un workflow de gestion
                const currentProject = getCurrentProjectId();
                if (currentProject) {
                    const formData = new FormData();
                    formData.append('project_id', currentProject);
                    formData.append('workflow_type', 'management');

                    const response = await fetch('http://localhost:8001/bi/trigger-n8n-workflow', {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();

                    if (result.success) {
                        showBINotification('n8n', `Workflow déclenché ! ID: ${result.execution_id}`, 'success');
                    } else {
                        showBINotification('n8n', 'Ouverture de l\'interface externe...', 'info');
                        setTimeout(() => window.open('https://n8n.io', '_blank'), 1000);
                    }
                } else {
                    showBINotification('n8n', 'Ouverture de l\'interface externe...', 'info');
                    setTimeout(() => window.open('https://n8n.io', '_blank'), 1000);
                }

            } catch (error) {
                console.error('Erreur gestion workflows:', error);
                showBINotification('n8n', 'Ouverture de l\'interface externe...', 'info');
                setTimeout(() => window.open('https://n8n.io', '_blank'), 1000);
            }
        }

        async function createWorkflow() {
            console.log('➕ Création d\'un nouveau workflow...');
            showBINotification('n8n', 'Création d\'un workflow automatisé...', 'info');

            try {
                const currentProject = getCurrentProjectId();
                if (!currentProject) {
                    showBINotification('n8n', 'Aucun projet sélectionné', 'warning');
                    return;
                }

                const formData = new FormData();
                formData.append('project_id', currentProject);
                formData.append('schedule', 'daily');
                formData.append('platforms', 'powerbi,tableau');

                const response = await fetch('http://localhost:8001/bi/create-automated-workflow', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    showBINotification('n8n', `Workflow automatisé créé ! ${result.workflow_name}`, 'success');
                    updateN8nMetrics();
                } else {
                    showBINotification('n8n', `Erreur: ${result.error || 'Création échouée'}`, 'error');
                }

            } catch (error) {
                console.error('Erreur création workflow:', error);
                showBINotification('n8n', 'Erreur de connexion au serveur', 'error');
            }
        }

        // 🎯 FONCTIONS UTILITAIRES
        function getCurrentProjectId() {
            // Essayer de récupérer le projet actuel depuis l'URL ou le contexte
            const urlParams = new URLSearchParams(window.location.search);
            const projectFromUrl = urlParams.get('project');

            if (projectFromUrl) {
                return projectFromUrl;
            }

            // Essayer de récupérer depuis le localStorage
            const lastProject = localStorage.getItem('lastSelectedProject');
            if (lastProject) {
                return lastProject;
            }

            // Utiliser un projet par défaut pour la démo
            return 'Test1'; // Vous pouvez adapter selon votre logique
        }

        function updateRealTimeMetrics(platform, data) {
            // Mettre à jour les métriques avec les vraies données
            console.log(`📊 Mise à jour métriques ${platform}:`, data);

            // Vous pouvez adapter cette fonction selon vos besoins
            if (platform === 'powerbi' && data.total_elements) {
                const reportsCount = document.querySelector('.bi-widget:nth-child(1) .bi-widget-content span:nth-child(2)');
                if (reportsCount) {
                    reportsCount.textContent = Math.ceil(data.total_elements / 100); // Exemple de calcul
                }
            }

            if (platform === 'tableau' && data.data_points) {
                const viewsCount = document.querySelector('.bi-widget:nth-child(2) .bi-widget-content div:nth-child(2) span:nth-child(2)');
                if (viewsCount) {
                    viewsCount.textContent = data.data_points;
                }
            }
        }

        // 🚀 FONCTION D'EXPORT MULTI-PLATEFORMES
        async function exportToAllPlatforms() {
            console.log('🚀 Export vers toutes les plateformes...');
            showBINotification('Multi-Export', 'Export vers toutes les plateformes...', 'info');

            try {
                const currentProject = getCurrentProjectId();
                if (!currentProject) {
                    showBINotification('Multi-Export', 'Aucun projet sélectionné', 'warning');
                    return;
                }

                const formData = new FormData();
                formData.append('project_id', currentProject);

                const response = await fetch('http://localhost:8001/bi/export-all-platforms', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    const successRate = result.summary.success_rate;
                    showBINotification('Multi-Export',
                        `Export terminé ! ${result.summary.successful_exports}/${result.summary.total_exports} plateformes (${successRate.toFixed(1)}%)`,
                        successRate > 50 ? 'success' : 'warning'
                    );

                    // Mettre à jour toutes les métriques
                    updatePowerBIMetrics();
                    updateTableauMetrics();
                    updateN8nMetrics();
                    updateERPMetrics();
                } else {
                    showBINotification('Multi-Export', 'Erreur lors de l\'export multi-plateformes', 'error');
                }

            } catch (error) {
                console.error('Erreur export multi-plateformes:', error);
                showBINotification('Multi-Export', 'Erreur de connexion au serveur', 'error');
            }
        }

        // 📊 FONCTIONS D'OUVERTURE DES WIDGETS
        function openPowerBIWidget() {
            console.log('📊 Ouverture du widget Power BI...');
            showBINotification('Power BI', 'Chargement du dashboard...', 'info');

            // Créer un popup Power BI intégré
            createBIPopup('Power BI Dashboard', `
                <div style="text-align: center; padding: 40px;">
                    <div style="width: 80px; height: 80px; margin: 0 auto 20px; background: linear-gradient(135deg, #f2c811 0%, #e6a500 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                        <i class="fab fa-microsoft" style="font-size: 30px; color: white;"></i>
                    </div>
                    <h3 style="color: #f2c811; margin-bottom: 20px;">Power BI Integration Active</h3>
                    <p style="color: var(--text-secondary); margin-bottom: 30px;">Connexion établie avec Power BI Service</p>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px;">
                        <div style="background: rgba(242, 200, 17, 0.1); padding: 20px; border-radius: 10px;">
                            <div style="font-size: 2em; font-weight: bold; color: #f2c811;">12</div>
                            <div style="color: var(--text-secondary);">Rapports actifs</div>
                        </div>
                        <div style="background: rgba(242, 200, 17, 0.1); padding: 20px; border-radius: 10px;">
                            <div style="font-size: 2em; font-weight: bold; color: #f2c811;">98%</div>
                            <div style="color: var(--text-secondary);">Disponibilité</div>
                        </div>
                    </div>

                    <button onclick="window.open('https://powerbi.microsoft.com', '_blank')" style="
                        background: linear-gradient(135deg, #f2c811 0%, #e6a500 100%);
                        border: none; color: white; padding: 12px 30px; border-radius: 25px;
                        cursor: pointer; font-weight: 600; margin-right: 10px;
                    ">Ouvrir Power BI</button>

                    <button onclick="syncPowerBI()" style="
                        background: rgba(242, 200, 17, 0.2); border: 1px solid #f2c811; color: #f2c811;
                        padding: 12px 30px; border-radius: 25px; cursor: pointer; font-weight: 600;
                    ">Synchroniser</button>
                </div>
            `);
        }

        function openTableauWidget() {
            console.log('📈 Ouverture du widget Tableau...');
            showBINotification('Tableau', 'Chargement des analytics...', 'info');

            createBIPopup('Tableau Analytics', `
                <div style="text-align: center; padding: 40px;">
                    <div style="width: 80px; height: 80px; margin: 0 auto 20px; background: linear-gradient(135deg, #1f77b4 0%, #ff7f0e 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-chart-bar" style="font-size: 30px; color: white;"></i>
                    </div>
                    <h3 style="color: #1f77b4; margin-bottom: 20px;">Tableau Server Connected</h3>
                    <p style="color: var(--text-secondary); margin-bottom: 30px;">Analytics et visualisations en temps réel</p>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px;">
                        <div style="background: rgba(31, 119, 180, 0.1); padding: 20px; border-radius: 10px;">
                            <div style="font-size: 2em; font-weight: bold; color: #1f77b4;">8</div>
                            <div style="color: var(--text-secondary);">Workbooks</div>
                        </div>
                        <div style="background: rgba(31, 119, 180, 0.1); padding: 20px; border-radius: 10px;">
                            <div style="font-size: 2em; font-weight: bold; color: #1f77b4;">24</div>
                            <div style="color: var(--text-secondary);">Vues actives</div>
                        </div>
                    </div>

                    <button onclick="window.open('https://tableau.com', '_blank')" style="
                        background: linear-gradient(135deg, #1f77b4 0%, #ff7f0e 100%);
                        border: none; color: white; padding: 12px 30px; border-radius: 25px;
                        cursor: pointer; font-weight: 600; margin-right: 10px;
                    ">Ouvrir Tableau</button>

                    <button onclick="syncTableau()" style="
                        background: rgba(31, 119, 180, 0.2); border: 1px solid #1f77b4; color: #1f77b4;
                        padding: 12px 30px; border-radius: 25px; cursor: pointer; font-weight: 600;
                    ">Publier</button>
                </div>
            `);
        }

        function openN8nWidget() {
            console.log('⚙️ Ouverture du widget n8n...');
            showBINotification('n8n', 'Chargement des workflows...', 'info');

            createBIPopup('n8n Automation Hub', `
                <div style="text-align: center; padding: 40px;">
                    <div style="width: 80px; height: 80px; margin: 0 auto 20px; background: linear-gradient(135deg, #ea4b71 0%, #ff6b6b 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-project-diagram" style="font-size: 30px; color: white;"></i>
                    </div>
                    <h3 style="color: #ea4b71; margin-bottom: 20px;">n8n Workflows Active</h3>
                    <p style="color: var(--text-secondary); margin-bottom: 30px;">Automatisation intelligente des processus BI</p>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px;">
                        <div style="background: rgba(234, 75, 113, 0.1); padding: 20px; border-radius: 10px;">
                            <div style="font-size: 2em; font-weight: bold; color: #ea4b71;">15</div>
                            <div style="color: var(--text-secondary);">Workflows</div>
                        </div>
                        <div style="background: rgba(234, 75, 113, 0.1); padding: 20px; border-radius: 10px;">
                            <div style="font-size: 2em; font-weight: bold; color: #ea4b71;">1.2K</div>
                            <div style="color: var(--text-secondary);">Exécutions/jour</div>
                        </div>
                    </div>

                    <button onclick="manageWorkflows()" style="
                        background: linear-gradient(135deg, #ea4b71 0%, #ff6b6b 100%);
                        border: none; color: white; padding: 12px 30px; border-radius: 25px;
                        cursor: pointer; font-weight: 600; margin-right: 10px;
                    ">Gérer Workflows</button>

                    <button onclick="createWorkflow()" style="
                        background: rgba(234, 75, 113, 0.2); border: 1px solid #ea4b71; color: #ea4b71;
                        padding: 12px 30px; border-radius: 25px; cursor: pointer; font-weight: 600;
                    ">Nouveau</button>
                </div>
            `);
        }

        function openERPWidget() {
            console.log('🏢 Ouverture du widget ERP...');
            showBINotification('ERP', 'Chargement des connexions...', 'info');

            createBIPopup('ERP Integration Center', `
                <div style="text-align: center; padding: 40px;">
                    <div style="width: 80px; height: 80px; margin: 0 auto 20px; background: linear-gradient(135deg, #28a745 0%, #20c997 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-database" style="font-size: 30px; color: white;"></i>
                    </div>
                    <h3 style="color: #28a745; margin-bottom: 20px;">ERP Systems Connected</h3>
                    <p style="color: var(--text-secondary); margin-bottom: 30px;">Intégration temps réel avec vos systèmes ERP</p>

                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 30px;">
                        <div style="background: rgba(40, 167, 69, 0.1); padding: 15px; border-radius: 10px;">
                            <div style="font-weight: bold; color: #28a745;">SAP</div>
                            <div style="color: var(--bi-success); font-size: 0.8em;">✓ Connecté</div>
                        </div>
                        <div style="background: rgba(40, 167, 69, 0.1); padding: 15px; border-radius: 10px;">
                            <div style="font-weight: bold; color: #28a745;">Oracle</div>
                            <div style="color: var(--bi-success); font-size: 0.8em;">✓ Connecté</div>
                        </div>
                        <div style="background: rgba(40, 167, 69, 0.1); padding: 15px; border-radius: 10px;">
                            <div style="font-weight: bold; color: #28a745;">Dynamics</div>
                            <div style="color: var(--bi-success); font-size: 0.8em;">✓ Connecté</div>
                        </div>
                    </div>

                    <button onclick="syncERP()" style="
                        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                        border: none; color: white; padding: 12px 30px; border-radius: 25px;
                        cursor: pointer; font-weight: 600; margin-right: 10px;
                    ">Synchroniser</button>

                    <button onclick="configureERP()" style="
                        background: rgba(40, 167, 69, 0.2); border: 1px solid #28a745; color: #28a745;
                        padding: 12px 30px; border-radius: 25px; cursor: pointer; font-weight: 600;
                    ">Configurer</button>
                </div>
            `);
        }

        // 🎨 FONCTIONS UTILITAIRES BI
        function createBIPopup(title, content) {
            // Supprimer les popups existants
            const existingPopup = document.querySelector('.bi-popup');
            if (existingPopup) {
                existingPopup.remove();
            }

            const popup = document.createElement('div');
            popup.className = 'bi-popup';
            popup.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                background: rgba(10, 10, 15, 0.9);
                backdrop-filter: blur(20px);
                z-index: 10001;
                display: flex;
                align-items: center;
                justify-content: center;
                animation: fadeIn 0.3s ease-out;
            `;

            popup.innerHTML = `
                <div style="
                    background: rgba(26, 26, 46, 0.95);
                    border-radius: 20px;
                    border: 1px solid rgba(99, 102, 241, 0.3);
                    max-width: 600px;
                    width: 90%;
                    max-height: 80vh;
                    overflow-y: auto;
                    position: relative;
                    animation: biSlideIn 0.4s ease-out;
                ">
                    <div style="
                        background: var(--bi-gradient);
                        padding: 20px;
                        border-radius: 20px 20px 0 0;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    ">
                        <h2 style="margin: 0; color: white; font-weight: 700;">${title}</h2>
                        <button onclick="closeBIPopup()" style="
                            background: rgba(255, 255, 255, 0.2);
                            border: none;
                            color: white;
                            width: 40px;
                            height: 40px;
                            border-radius: 50%;
                            cursor: pointer;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            transition: all 0.3s ease;
                        " onmouseover="this.style.background='rgba(255, 255, 255, 0.3)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.2)'">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div>${content}</div>
                </div>
            `;

            document.body.appendChild(popup);

            // Fermer en cliquant à l'extérieur
            popup.addEventListener('click', (e) => {
                if (e.target === popup) {
                    closeBIPopup();
                }
            });
        }

        function closeBIPopup() {
            const popup = document.querySelector('.bi-popup');
            if (popup) {
                popup.style.animation = 'fadeOut 0.3s ease-out';
                setTimeout(() => {
                    popup.remove();
                }, 300);
            }
        }

        function showBINotification(system, message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = 'bi-notification';

            const colors = {
                info: '#6366f1',
                success: '#10b981',
                warning: '#f59e0b',
                error: '#ef4444'
            };

            const icons = {
                info: 'fas fa-info-circle',
                success: 'fas fa-check-circle',
                warning: 'fas fa-exclamation-triangle',
                error: 'fas fa-times-circle'
            };

            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: rgba(26, 26, 46, 0.95);
                border: 1px solid ${colors[type]};
                border-radius: 15px;
                padding: 20px;
                max-width: 400px;
                z-index: 10002;
                backdrop-filter: blur(15px);
                animation: slideInRight 0.4s ease-out;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            `;

            notification.innerHTML = `
                <div style="display: flex; align-items: center; gap: 15px;">
                    <div style="
                        width: 40px;
                        height: 40px;
                        border-radius: 50%;
                        background: ${colors[type]};
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: white;
                    ">
                        <i class="${icons[type]}"></i>
                    </div>
                    <div style="flex: 1;">
                        <div style="font-weight: 600; color: ${colors[type]}; margin-bottom: 5px;">${system}</div>
                        <div style="color: var(--text-secondary); font-size: 0.9em;">${message}</div>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()" style="
                        background: none;
                        border: none;
                        color: var(--text-secondary);
                        cursor: pointer;
                        padding: 5px;
                        border-radius: 50%;
                        width: 30px;
                        height: 30px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    ">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            document.body.appendChild(notification);

            // Auto-remove après 5 secondes
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.style.animation = 'slideOutRight 0.4s ease-out';
                    setTimeout(() => {
                        notification.remove();
                    }, 400);
                }
            }, 5000);
        }

        // Fonctions de configuration
        function configurePowerBI() {
            showBINotification('Power BI', 'Ouverture de la configuration...', 'info');
        }

        function configureTableau() {
            showBINotification('Tableau', 'Ouverture de la configuration...', 'info');
        }

        function configureERP() {
            showBINotification('ERP', 'Ouverture de la configuration...', 'info');
        }

        // Ajouter les animations CSS manquantes
        const biAnimationStyles = document.createElement('style');
        biAnimationStyles.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(biAnimationStyles);

        // 📊 FONCTIONS ADDITIONNELLES BI
        async function loadBIHistory() {
            console.log('📊 Chargement de l\'historique BI...');
            showBINotification('Historique', 'Chargement de l\'historique...', 'info');

            try {
                const response = await fetch('http://localhost:8001/bi/sync-history?limit=20');
                const result = await response.json();

                if (result.history) {
                    const historyHtml = result.history.map(sync => `
                        <div style="padding: 10px; margin: 5px 0; background: rgba(26, 26, 46, 0.5); border-radius: 8px; border-left: 3px solid ${sync.status === 'success' ? '#10b981' : '#ef4444'};">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span style="font-weight: 600; color: var(--text-primary);">${sync.platform}</span>
                                <span style="font-size: 0.8em; color: var(--text-secondary);">${new Date(sync.timestamp).toLocaleString()}</span>
                            </div>
                            <div style="color: var(--text-secondary); font-size: 0.9em; margin-top: 5px;">
                                Projet: ${sync.project_id} • ${sync.message}
                            </div>
                        </div>
                    `).join('');

                    createBIPopup('Historique des Synchronisations', `
                        <div style="padding: 20px; max-height: 400px; overflow-y: auto;">
                            <div style="margin-bottom: 20px; text-align: center;">
                                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                                    <div style="background: rgba(99, 102, 241, 0.1); padding: 15px; border-radius: 10px;">
                                        <div style="font-size: 1.5em; font-weight: bold; color: var(--bi-primary);">${result.statistics.total_synchronizations}</div>
                                        <div style="color: var(--text-secondary); font-size: 0.9em;">Total</div>
                                    </div>
                                    <div style="background: rgba(16, 185, 129, 0.1); padding: 15px; border-radius: 10px;">
                                        <div style="font-size: 1.5em; font-weight: bold; color: var(--bi-success);">${result.statistics.successful_synchronizations}</div>
                                        <div style="color: var(--text-secondary); font-size: 0.9em;">Réussies</div>
                                    </div>
                                    <div style="background: rgba(245, 158, 11, 0.1); padding: 15px; border-radius: 10px;">
                                        <div style="font-size: 1.5em; font-weight: bold; color: var(--bi-warning);">${result.statistics.success_rate.toFixed(1)}%</div>
                                        <div style="color: var(--text-secondary); font-size: 0.9em;">Taux de succès</div>
                                    </div>
                                </div>
                            </div>
                            <h4 style="color: var(--text-primary); margin-bottom: 15px;">Dernières Synchronisations</h4>
                            ${historyHtml || '<p style="color: var(--text-secondary); text-align: center;">Aucun historique disponible</p>'}
                        </div>
                    `);
                } else {
                    showBINotification('Historique', 'Aucun historique disponible', 'warning');
                }

            } catch (error) {
                console.error('Erreur chargement historique:', error);
                showBINotification('Historique', 'Erreur de chargement', 'error');
            }
        }

        async function checkBIStatus() {
            console.log('🔍 Vérification du statut BI...');
            showBINotification('Statut', 'Vérification des connecteurs...', 'info');

            try {
                const response = await fetch('http://localhost:8001/bi/status');
                const result = await response.json();

                if (result.status === 'operational') {
                    const connectorsHtml = Object.entries(result.connectors).map(([name, connector]) => `
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px; margin: 5px 0; background: rgba(26, 26, 46, 0.5); border-radius: 8px;">
                            <div>
                                <div style="font-weight: 600; color: var(--text-primary);">${name}</div>
                                <div style="font-size: 0.8em; color: var(--text-secondary);">${connector.type.toUpperCase()}</div>
                            </div>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <div style="width: 8px; height: 8px; border-radius: 50%; background: ${connector.active ? '#10b981' : '#ef4444'};"></div>
                                <span style="color: ${connector.active ? '#10b981' : '#ef4444'}; font-size: 0.9em;">
                                    ${connector.active ? 'Actif' : 'Inactif'}
                                </span>
                            </div>
                        </div>
                    `).join('');

                    createBIPopup('Statut Business Intelligence', `
                        <div style="padding: 20px;">
                            <div style="text-align: center; margin-bottom: 25px;">
                                <div style="width: 60px; height: 60px; margin: 0 auto 15px; background: linear-gradient(135deg, #10b981, #059669); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-check" style="color: white; font-size: 24px;"></i>
                                </div>
                                <h3 style="color: var(--bi-success); margin-bottom: 10px;">Système Opérationnel</h3>
                                <p style="color: var(--text-secondary);">${result.active_connectors}/${result.total_connectors} connecteurs actifs</p>
                            </div>

                            <h4 style="color: var(--text-primary); margin-bottom: 15px;">État des Connecteurs</h4>
                            ${connectorsHtml}

                            <div style="margin-top: 20px; padding: 15px; background: rgba(99, 102, 241, 0.1); border-radius: 10px; border-left: 3px solid var(--bi-primary);">
                                <div style="font-weight: 600; color: var(--bi-primary); margin-bottom: 5px;">ℹ️ Information</div>
                                <div style="color: var(--text-secondary); font-size: 0.9em;">
                                    Tous les connecteurs sont prêts pour l'export automatique.
                                    Utilisez les boutons de synchronisation pour tester les connexions.
                                </div>
                            </div>
                        </div>
                    `);

                    showBINotification('Statut', `${result.active_connectors}/${result.total_connectors} connecteurs actifs`, 'success');
                } else {
                    showBINotification('Statut', 'Système BI non opérationnel', 'error');
                }

            } catch (error) {
                console.error('Erreur vérification statut:', error);
                showBINotification('Statut', 'Erreur de connexion au serveur', 'error');
            }
        }

        console.log('🚀 Business Intelligence Module chargé avec succès !');
    </script>
</body>

</html>
